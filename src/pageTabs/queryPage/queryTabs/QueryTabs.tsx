import React, { useEffect, useRef, useState, useCallback } from 'react'
import { useSelector, useDispatch, useRequest } from 'src/hook'
import { Dropdown, Menu, Modal, Popconfirm, Tabs, Tooltip, Typography, message } from 'antd'
import { editor } from 'monaco-editor'
import { <PERSON>con<PERSON>nt, LazyWrapper } from 'src/components'
import {
  setActiveTabKey,
  removeQueryPane,
  addPane,
  resetQueryTabs,
  resetOtherQueryTabs
} from './queryTabsSlice'
import { setVisibleSdt, setVisibleNodeDetail } from '../queryPageSlice'
import { ResultTabs } from '../resultTabs/ResultTabs'
import { paneInfoMapSelector, removeAllOrCloseOtherResultTab } from '../resultTabs/resultTabsSlice'
import { resetLogs } from 'src/store/extraSlice/logsSlice'
import {
  CreateTablePane,
  CreateNewTablePane,
  MonacoPane,
  GridPane,
  TerminalPane,
  CreateViewPane,
  DesignNewTablePane,
  CompileInvalidPane,
  ViewViewStructurePane,
  ObCreateViewPane,
  ViewNodeTypeEndWithGroupPane,
  DesignNewViewPane,
  RedisViewBasicInfoPane
} from './index'
import classNames from 'classnames'
import styles from './index.module.scss'
import { removeDesignInfo } from 'src/pageTabs/queryPage/queryTabs/designNewTablePane/designTableSlice'
import { useEditorInstance } from 'src/components/BaseEditor/useEditorInstance'
import { getExecutedSql } from 'src/api'
import { ErrorBoundary } from 'src/components'
import {setDataSourceMetaDispatch} from "../../../store/extraSlice/dataSourceMetaSlice";
import { updateMonaco } from './queryTabsSlice'
import { getInfoFromPath } from 'src/util'
import DiffEditor from 'src/components/DiffEditor'
import ChooseCompareDiffLists from 'src/components/ChooseCompareDiffLists'
import PaneRenameModal from './components/PaneRenameModal'
import ConnColorSettingModal from './components/ConnColorSettingModal'
import { hexToRgbStr } from 'src/components/ColorPicker/util'
import { CloseOutlined } from '@ant-design/icons'
import { useTranslation } from 'react-i18next'

const { Text } = Typography;

//当前只支持添加表的数据源
let SUPPORT_ADD_TABLE_DATASOURCE = ['MySQL', 'MariaDB', 'Oracle', 'PostgreSQL', 'SQLServer', 'DamengDB', 'OceanBase', 'PolarDB','StarRocks','HighGo'];
interface ICompareInfo{
  originalTxt: string;
  modifiedTxt: string;
  originalName: string;
  modifiedName: string;
  key: string
}
export const QueryTabs = () => {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const { tabKeyList, tabInfoMap, activeTabKey, paneInfoMap } = useSelector(
    (state) => state.queryTabs,
  )
  const curPaneInfoMap = useSelector(paneInfoMapSelector)
  const { visibleSdt, visibleQueryTabs } = useSelector(
    (state) => state.queryPage,
  )
  const { theme, editorPromptOnClose } = useSelector(
    (state) => state.appearance,
  )
  const { selectedNode } = useSelector((state) => state.sdt)
  const tabs = tabKeyList.map((key) => tabInfoMap[key])
  const editorTabs = tabs.filter(({ paneType }) =>
    ['monaco', 'plSql', 'tSql', 'obCreateView', 'createFnAndProcedure'].includes(paneType),
  )
  const activePaneType = tabInfoMap[activeTabKey]?.paneType
  const resultPageSize = tabInfoMap[activeTabKey]?.resultPageSize || 100;
  const isMonaco = activePaneType === 'monaco'
  const isPlSql = activePaneType === 'plSql'
  const isTsql = activePaneType === 'tSql'
  // 新建函数（组），新建存储过程（组）
  const isCreateFnAndProcedure = activePaneType === 'createFnAndProcedure'
  // 版本对比
  const compareVersion = activePaneType === 'diff'
  // ob创建视图
  const [createMonacoPane, setCreateMonacoPane] = useState<any>({})
  const [editorInstance] = useEditorInstance()
  // model cache map ref
  const modelCacheRef = useRef<Map<string, editor.ITextModel>>(new Map())
  const [renameVisible, setRenameVisible] = useState<boolean>(false) // 修改tab名称弹窗
  const [connColorVisible, setConnColorVisible] = useState<boolean>(false) // 设置连接颜色弹窗
  const [paneRenameProps, setPaneRenameProps] = useState<any>({
    tabKey: undefined,
    tabName: undefined,
  }) // 修改tab名称弹窗参数
  const [connColorTabProps, setConnColorTabProps] = useState<any>({
    tabKey: undefined,
    tabColor: undefined,
  }) // 设置连接颜色参数
  const [compareInfo, setCompareInfo] = useState<ICompareInfo[]>([])

  useEffect(() => {
    return () => {
      if (!modelCacheRef.current) return
      modelCacheRef.current?.forEach((model) => {
        model.dispose()
      })
      // eslint-disable-next-line react-hooks/exhaustive-deps
      modelCacheRef.current?.clear()
    }
  }, [])

  useEffect(()=>{
    // 发请求
    const type = paneInfoMap[activeTabKey]?.connectionType;
    if (!type)  return;
    dispatch(setDataSourceMetaDispatch(type));
  },[activeTabKey])

  /* 关闭 tab 页之前，检查是否有未提交事务 */
  const { run: getIsInTransaction } = useRequest(getExecutedSql, {
    manual: true,
    // 这里需要加 fetchKey，否则新请求会让旧请求持续 pending
    fetchKey: ({ tabKey }) => tabKey,
    formatResult: (data) => data && data[0],
  })

  useEffect(() => {
    if (!tabs.length) {
      dispatch(addPane({}))
    }
  }, [dispatch, tabs])

  /* 获取 pane 是否存在事务及相关信息 */
  const getInTransactionPaneInfo = async (targetKey: string) => {
    /* monaco 类型 pane，检查是否存在事务 */
    const targetTabInfo = tabInfoMap[targetKey]
    const targetPaneInfo = paneInfoMap[targetKey]
    const { key, connectionId, databaseName } = targetPaneInfo
    if (targetTabInfo.paneType === 'monaco' && connectionId) {
      const isInTransaction = await getIsInTransaction({
        tabKey: key,
        connectionId,
        databaseName,
      })
      if (isInTransaction) {
        return {
          isInTransaction: true,
          key,
          tabName: targetTabInfo.tabName,
        }
      }
    }
    return {
      isInTransaction: false,
      key,
      tabName: targetTabInfo.tabName,
    }
  }

  const handleNewQuery = () => {
    const { key, connectionId, connectionType, nodePath } = selectedNode ?? {}
    // 若选择的节点是redis数据库节点，则直接新建一个空的pane
    if(connectionType === 'Redis') {
      dispatch(
        addPane({})
      );
      return; 
    }
    // 若没有选中节点 使用当前active pane 的信息
    if (key === undefined) {
      const { connectionId, connectionType, databaseName, schemaName } = curPaneInfoMap ?? {};
      dispatch(
        addPane({
          connectionId,
          connectionType,
          databaseName,
          schemaName,
        })
      );
      return;
    }
    // 新建查询并自动选择树节点所在的执行上下文
    const databaseName = getInfoFromPath(nodePath, "database", connectionType);
    const schemaName = getInfoFromPath( nodePath, "schema", connectionType);
    dispatch(
      addPane({
        connectionId,
        connectionType,
        databaseName,
        schemaName,
      })
    );
  }

  // 解析tab标签页的颜色
  const getTabColor = (pane:any):any => {
    const { tabColor:hex } = pane
    if(!hex) return null
    const rgb = hexToRgbStr(hex?.hex, hex?.opacity, 'rgb')
    const rgba = hexToRgbStr(hex?.hex, hex?.opacity, 'rgba')
    return {
      hex,
      rgb,
      rgba
    }
  }

  const disposeModels = (keys: string[]) => {
    keys.forEach(key => {
      const model = modelCacheRef.current.get(key)
      if (model) {
        model.dispose()
        modelCacheRef.current.delete(key)
      }
    })
  }

  //多处使用
  const commonClearQuertTabsAction = () => {
    dispatch(resetQueryTabs());
    dispatch(removeAllOrCloseOtherResultTab({closedTabType: 'all'}));
    // 清理所有日志数据，避免内存泄漏
    dispatch(resetLogs(undefined));
    modelCacheRef.current.forEach((model) => model.dispose())
    modelCacheRef.current.clear()
  }

  //关闭所有窗口、关闭其他窗口
  const handleClosePointSqlTabs = async(pointTabKeys: string[], type: 'all' | 'other', curTabKey?: string) => {
    try {
      const allPaneInfoByInTransaction = await Promise.all(
        pointTabKeys.map(getInTransactionPaneInfo),
      )
      const messageInfo = allPaneInfoByInTransaction?.reduce(
        (total, it) => {
          if (it.isInTransaction) {
            total = `${total}${total && '、'}${it.tabName}`
          }
          return total
        },
        '',
      )
      if (messageInfo) {
        message.error(
          `${messageInfo} ${t('sdo_close_windows_error')}`,
        )
        return
      }
      if (type === 'all') {
        commonClearQuertTabsAction();
      }else {
        curTabKey && dispatch(resetOtherQueryTabs(curTabKey));
        dispatch(removeAllOrCloseOtherResultTab({tabKey: curTabKey, closedTabType: 'notCurrentSelected'}))
        // 清理其他 Tab 的日志数据
        dispatch(resetLogs(pointTabKeys));
        disposeModels(pointTabKeys);
      }
     
    } catch (error) {
      Modal.confirm({
        content: t('sdo_force_close_confirmation'),
        onOk: () => {
          commonClearQuertTabsAction();
        },
      })
    }
  }
 
  // tab页的右键菜单click事件
  const handleMenuClick = (clickInfo: any, tabItem: any) => {
    const {key: clickKey } = clickInfo
    switch (clickKey) {
      case 'rename':
        setPaneRenameProps({
          tabKey: tabItem.key,
          tabName: tabItem.tabName,
        })
        setRenameVisible(true);
        break;
      case 'setConnColor':
        setConnColorVisible(true);
        setConnColorTabProps({
          tabKey: tabItem.key,
          tabColor: tabItem.tabColor
        })
        break;
        case 'closeOthers':
          const otherTabKeys = tabKeyList.filter((it: any) => it !== tabItem.key)
          handleClosePointSqlTabs(otherTabKeys, 'other',tabItem.key);
        break;
      default:
        break;
    }
  }

  // SDT 全屏显示处理函数，使用 useCallback 避免闭包问题和内存泄露
  const handleSdtFullscreenToggle = useCallback(() => {
    dispatch(setVisibleSdt(false))
    dispatch(setVisibleNodeDetail(false))
  }, [dispatch])

  // SDT 退出全屏处理函数，使用 useCallback 避免闭包问题和内存泄露
  const handleSdtExitFullscreen = useCallback(() => {
    dispatch(setVisibleSdt(true))
  }, [dispatch])

  // tab标题的右键菜单
  const tabContextMenu = (item: any) => {
    return (<Menu onClick={(info: any) => handleMenuClick(info, item)} >
      <Menu.Item key={'rename'}>
        {t('sdo_rename')}
      </Menu.Item>
      <Menu.Item key={'setConnColor'}>
        {t('sdo_set_connection_color')}
      </Menu.Item>
      <Menu.Item key={'closeOthers'}>
        {t('sdo_set_connection_tag')}
      </Menu.Item>
    </Menu>
    )
  }

  const extraOperations = (
    <div className={styles.extraOperations}>
      <Tooltip title={t('sdo_create_new_query')}>
        <Iconfont
          className={styles.extraOperationIcon}
          type="icon-query"
          onClick={handleNewQuery}
        />
      </Tooltip>
      {visibleSdt ? (
        <Iconfont
          className={styles.extraOperationIcon}
          type="icon-fullscreen"
          onClick={handleSdtFullscreenToggle}
        />
      ) : (
        <Iconfont
          className={styles.extraOperationIcon}
          type="icon-fullscreen-exit"
          onClick={handleSdtExitFullscreen}
        />
      )}
      <Tooltip title={t('sdo_close_all_windows')} placement="left">
        <Popconfirm
          title={t('sdo_close_all_windows_confirmation')}
          onConfirm={async () => {
            handleClosePointSqlTabs(tabKeyList, 'all');
          }}
          placement="bottomRight"
        >
          <Iconfont
            type="icon-closeAll"
            className={styles.extraOperationIcon}
          />
        </Popconfirm>
      </Tooltip>
    </div>
  )

  const handleCreateMonacoPane = async(sql: string) => {
    await setCreateMonacoPane((t: any)=>{
      return {
        ...t,
        [activeTabKey]: true
      }
    })
    dispatch(updateMonaco({ key: activeTabKey, value: sql }))
  }

  // 设置编译对象，创建函数（组），存储过程（组）的标题
  const getCompileOrCreatePaneTitle = (connectionType: string='', nodeType: string='', prefix: string='') => {
    let title = t('sdo_invalid_object');
    if(['OceanBaseMySQL', 'OceanBase'].includes(connectionType)){
      switch (nodeType) {
        case 'function':
        case 'functionGroup':
          title = t('sdo_function');
          break;
        case 'procedure':
        case 'storedProcedureGroup':
          title = t('sdo_stored_procedure');
          break;
      }
    }
    return  prefix + title
  }

  const handleTabsEdit = async(targetKey: React.MouseEvent | React.KeyboardEvent | string) => {
    if (typeof targetKey !== 'string') return
    const removeTab = () => {
      dispatch(removeQueryPane(targetKey))
      dispatch(removeDesignInfo(targetKey))
      //tab关闭删除缓存的执行结果数据
      dispatch(removeAllOrCloseOtherResultTab({tabKey: targetKey, closedTabType: 'current'}))
      // 清理对应的日志数据，避免内存泄漏
      dispatch(resetLogs(targetKey))
      // 注销 model, 销毁 cache
      modelCacheRef.current.get(targetKey)?.dispose()
      modelCacheRef.current.delete(targetKey)
    }

    /* 检查是否进行中的事务 */
    try {
      const { isInTransaction, tabName } =
        await getInTransactionPaneInfo(targetKey)
      if (isInTransaction) {
        message.error(`${tabName} ${t('sdo_close_windows_error')}`)
        return
      }
    } catch (error) {
      Modal.confirm({
        title: t('sdo_force_close_confirmation'),
        onOk: () => removeTab(),
      })
      return
    }

    /* 检查是否是 设计表 */
    if (['designTable', 'designView'].includes(tabInfoMap[activeTabKey].paneType)) {
      Modal.confirm({
        title: t('sdo_close_confirmation'),
        onOk: () => removeTab(),
      })
      return
    }

    if (!editorPromptOnClose) return removeTab()
    const getHasContent = () =>
      Boolean(editorInstance?.getModel()?.getValue().trim())
    if (!getHasContent()) return removeTab()
    Modal.confirm({
      title: t('sdo_unsaved_changes_confirmation'),
      onOk: () => removeTab(),
      okText: t('sdo_ok'),
      cancelText: t('sdo_cancel')
    })
  }

  const handleTabsChange = (key: string) => {
    dispatch(setActiveTabKey(key))
  }

  const handleCompare = (selectItems: any[], key: string) => {
    const originalTxt = selectItems[0]?.statement
    const modifiedTxt = selectItems[1]?.statement
    const originalName = selectItems[0]?.name
    const modifiedName = selectItems[1]?.name
    setCompareInfo((info: any[])=>{
      // 如果key存在,则替换新的originalTxt和modifiedTxt
      if(info?.some(i=>i?.key === key)){
        return info.map(i=>i?.key === key ? {...i, originalTxt, modifiedTxt} : i) 
      }
      return [...info, {key, originalTxt, modifiedTxt, originalName, modifiedName}]
    })
  }

  const curOriginalTxt = compareInfo?.filter(i=>i?.key === activeTabKey)?.[0]?.originalTxt ?? ''
  const curModifiedTxt = compareInfo?.filter(i=>i?.key === activeTabKey)?.[0]?.modifiedTxt ?? ''
  const curOriginalName = compareInfo?.filter(i=>i?.key === activeTabKey)?.[0]?.originalName ?? ''
  const curModifiedName = compareInfo?.filter(i=>i?.key === activeTabKey)?.[0]?.modifiedName ?? ''
  
  return (
    <section className={styles.queryTabsWrapper}>
      <div className={styles.queryTabs}>
        <Tabs
          hideAdd
          // type="editable-card"
          id={'queryTabs'}
          activeKey={activeTabKey}
          onChange={handleTabsChange}
          // onEdit={handleTabsEdit}
          tabBarExtraContent={extraOperations}
          tabBarStyle={{ marginBottom: 0 }}
          tabBarGutter={0}
          style={{ minHeight: 40 }}
          className={classNames(!visibleQueryTabs && styles.hide, styles.queryTabsNode)}
        >
          {
            tabs.map((tabItem: any) => {
              const { key, tabName, paneType } = tabItem
              // tooltip title 去除图标
              let curTitle = tabName
              const getTab = () => {
                // 版本对比
                if (paneType === 'diff') {
                  return (
                    <>
                      <Iconfont
                        className={styles.tabContentIcon}
                        type="icon-cloud-file"
                        style={{ color: '#5794ff' }}
                      ></Iconfont>
                      <span>{tabName}</span>
                    </>
                  )
                }
                if (paneInfoMap[key].serverFile) {
                  return (
                    <>
                      <Iconfont
                        className={styles.tabContentIcon}
                        type="icon-cloud-file"
                        style={{ color: '#5794ff' }}
                      ></Iconfont>
                      <span>{tabName}</span>
                    </>
                  )
                }
                switch (paneType) {
                  case 'terminal':
                    return (
                      <>
                        <Iconfont
                          className={styles.tabContentIcon}

                          type="icon-terminal"></Iconfont>
                        <span>{tabName}</span>
                      </>
                    )
                  case 'addTable':
                    curTitle=t('sdo_add_table');
                    return (
                      <>
                        <Iconfont
                          className={styles.tabContentIcon}
                          type="icon-plus-square"
                          style={{ color: '#ffbc57' }}
                        ></Iconfont>
                        <span>{t('sdo_add_table')}</span>
                      </>
                    )
                  case 'designTable':
                    curTitle=t('sdo_design_table')
                    return (
                      <>
                        <Iconfont
                          className={styles.tabContentIcon}
                          type="icon-design-table"
                          style={{ color: '#ffbc57' }}
                        ></Iconfont>
                        <span>{t('sdo_design_table')}</span>
                      </>
                    )
                  case 'viewTableStructure':
                    return (
                      <>
                        <Iconfont
                          className={styles.tabContentIcon}
                          type="icon-table-structure"
                          style={{ color: '#05d174' }}
                        ></Iconfont>
                        <span>{tabName}</span>
                      </>
                    )
                  case 'addView':
                    curTitle=t('sdo_add_view')
                    return (
                      <>
                        <Iconfont
                          className={styles.tabContentIcon}
                          type="icon-plus-square"
                          style={{ color: '#ffbc57' }}
                        ></Iconfont>
                        <span>{t('sdo_add_view')}</span>
                      </>
                    )
                  case 'obCreateView':
                    curTitle=t('sdo_create_view')
                    return (
                      <>
                        <Iconfont
                          className={styles.tabContentIcon}
                          type="icon-plus-square"
                          style={{ color: '#ffbc57' }}
                        ></Iconfont>
                        <span>{t('sdo_create_view')}</span>
                      </>
                    )
                  case 'designView':
                    curTitle=t('sdo_design_view')
                    return (
                      <>
                        <Iconfont
                          className={styles.tabContentIcon}
                          type="icon-shejishitu"
                          style={{ color: '#ffbc57' }}
                        ></Iconfont>
                        <span>{t('sdo_design_view')}</span>
                      </>
                    )
                  case 'monaco':
                  case 'plSql':
                  case 'tSql':
                    return (
                      <>
                        <Iconfont
                          className={styles.tabContentIcon}
                          type="icon-sql"
                          style={{ color: '#0096d8' }}
                        ></Iconfont>
                        <span>{tabName}</span>
                      </>
                    )
                  case 'grid':
                    return (
                      <>
                        <Iconfont
                          className={styles.tabContentIcon}
                          type="icon-redisKey"></Iconfont>
                        <span>{tabName}</span>
                      </>
                    )
                  case 'compileInvalidObjects': {
                    // 编译无效对象、编译函数（组），编译存储过程（组）title
                    const title = getCompileOrCreatePaneTitle(paneInfoMap[key]?.connectionType, paneInfoMap[key]?.nodeType, t('sdo_compile'))
                    curTitle = title
                    return (
                      <>
                        <Iconfont
                          className={styles.tabContentIcon}
                          type="icon-terminal"></Iconfont>
                        <span>{title}</span>
                      </>
                    )
                  }
                  // 查看视图结构
                  case 'viewViewStructureMenu':
                    return (
                      <>
                        <Iconfont
                          className={styles.tabContentIcon}
                          type="icon-table-structure"
                          style={{ color: '#05d174' }}
                        ></Iconfont>
                        <span>{tabName}</span>
                      </>
                    )
                  // 新建函数（组），新建存储过程（组）tab标题
                  case 'createFnAndProcedure': {
                    const newTitle = getCompileOrCreatePaneTitle(paneInfoMap[key]?.connectionType, paneInfoMap[key]?.nodeType, t('sdo_create'))
                    curTitle = newTitle
                    return (
                      <>
                        <Iconfont
                          className={styles.tabContentIcon}
                          type={`icon-${paneInfoMap[key].nodeType}`}
                          style={{ color: '#5794ff' }}
                        ></Iconfont>
                        <span>{newTitle}</span>
                      </>
                    )
                  }
                  case 'openTab':
                    return (
                      <>
                        <Iconfont
                          className={styles.tabContentIcon}
                          type={`icon-${paneInfoMap[key]?.nodeType}`}
                          style={{ color: '#ffbc57' }}
                        ></Iconfont>
                        <span>{tabName}</span>
                      </>
                    )
                  // redis 查看基本信息
                  case 'viewBasicInfo':
                    curTitle = t('sdo_view_basic_information')
                    return (
                      <>
                        <Iconfont
                          className={styles.tabContentIcon}
                          type={'icon-connection-Redis'}></Iconfont>
                        <span>{t('sdo_view_basic_information')}</span>
                      </>
                    )
                  default:
                    return tabName
                }
              }
              const tab = getTab()
              const tabColorObj = getTabColor(paneInfoMap[key])

              return (
                <Tabs.TabPane
                  key={key}
                  tab={
                    <Dropdown overlay={tabContextMenu({ ...tabItem,tabColor:tabColorObj?.hex})} trigger={['contextMenu']}>
                      <Tooltip title={curTitle}>
                      <div  className={styles.borderDiv}>
                        <div
                          className={classNames(
                            styles.queryTabTitle,
                            key === activeTabKey && styles.queryTabTitleActive,
                          )}
                          style={{
                            background: tabColorObj?.rgba
                          }}
                        >
                          <Text
                            className={styles.tabContent}
                            ellipsis
                          >
                            <ErrorBoundary>
                              {tab}
                            </ErrorBoundary>
                          </Text>
                          {/* 删除图标 */}
                          <div className={styles.delDiv}>
                            <CloseOutlined
                              onClick={(e: any) => {
                                // 阻止冒泡
                                e.stopPropagation()
                                handleTabsEdit(key)
                              }}
                              className={styles.tabCloseIcon}
                            />
                          </div>
                        </div>
                      </div>
                      </Tooltip>
                    </Dropdown>
                  }
                >
                  { 
                    compareVersion && 
                    <>
                      <div className={styles.compareNameLine}>
                        <span style={{width: '48%', padding: '6px 12px'}}>{t('sdo_version_name')}:{curOriginalName}</span>
                        <span style={{width: '52%', padding: '6px 12px'}}>{t('sdo_version_name')}:{curModifiedName}</span>
                      </div>
                      <DiffEditor 
                        originalTxt={curOriginalTxt}
                        modifiedTxt={curModifiedTxt}
                        theme={theme}
                        wrapStyle={{height: `calc(100vh - 350px)`}}
                      />
                      <ChooseCompareDiffLists 
                        nodeInfo={paneInfoMap[key]}
                        theme={theme}
                        handleCompare={(params)=>handleCompare(params, key)}
                      />
                    </>
                  }
                </Tabs.TabPane>
              )
            })
          }
        </Tabs>
        {/* 非新建函数（组），新建存储过程（组）; 非创建视图回调场景 */}
        {
          !compareVersion &&
          !isCreateFnAndProcedure &&
          !createMonacoPane?.[activeTabKey] &&
          tabs.map(({ key, paneType }) => {
            const {
              connectionType = 'MySQL',
              connectionId = 0,
              databaseName = '',
              nodePath,
              nodeName,
              nodeType,
              nodePathWithType,
              schemaName,
              groupId
            } = paneInfoMap[key]
            const databaseInfo = {
              connectionType,
              connectionId,
              databaseName,
              schemaName,
              nodePath,
              nodeName,
              nodeType,
              nodePathWithType,
              groupId,
            }
            const getPane = () => {
              switch (paneType) {
                case 'terminal':
                  return <TerminalPane connectionId={connectionId} />
                case 'addTable':
                  //改版
                  if (SUPPORT_ADD_TABLE_DATASOURCE.includes(paneInfoMap[key]?.connectionType || '')) {
                    return (
                      <CreateNewTablePane
                        databaseInfo={databaseInfo}
                        queryTabKey={key}
                      />
                    )
                  }
                  return (
                    <CreateTablePane
                      databaseInfo={databaseInfo}
                      queryTabKey={key}
                    />
                  )
                case 'viewTableStructure':
                  // 改版后的
                  return (
                    <DesignNewTablePane
                      databaseInfo={databaseInfo}
                      queryTabKey={key}
                      modeType='viewTableStructure'
                    />
                  )
                case 'designTable':
                  // 改版
                  return <DesignNewTablePane
                    databaseInfo={databaseInfo}
                    queryTabKey={key}
                    modeType='designTable'
                  />
                case 'addView':
                  return (
                    <CreateViewPane
                      databaseInfo={databaseInfo}
                      queryTabKey={key}
                    />
                  )
                case 'grid':
                  return <GridPane databaseInfo={databaseInfo} />
                case 'compileInvalidObjects':
                  return (
                    <CompileInvalidPane
                      databaseInfo={databaseInfo}
                      title={getCompileOrCreatePaneTitle(paneInfoMap[key]?.connectionType, paneInfoMap[key]?.nodeType, t('sdo_compile'))}
                      nodeType={paneInfoMap[key]?.nodeType}
                    />
                  )
                case 'viewViewStructureMenu':
                  return (
                    <ViewViewStructurePane
                      databaseInfo={databaseInfo}
                      queryTabKey={key}
                    />
                  )
                // ob新建视图
                case 'obCreateView':
                  return (
                    <ObCreateViewPane
                      databaseInfo={databaseInfo}
                      callback={handleCreateMonacoPane}
                    />
                  )
                // 设计视图
                case 'designView':
                  return (
                    <DesignNewViewPane
                      databaseInfo={databaseInfo}
                      queryTabKey={key}
                    />
                  )
                case 'openTab':
                  return (
                    <ViewNodeTypeEndWithGroupPane
                      databaseInfo={databaseInfo}
                      activeTabKey={activeTabKey}
                    />
                  )
                // redis 查看基本信息
                case 'viewBasicInfo':
                  return (
                    <RedisViewBasicInfoPane
                      connectionId={connectionId}
                      queryTabKey={key}
                      activeTabKey={activeTabKey}
                    />
                  )
                default:
                  return null
              }
            }

            return (
              <LazyWrapper
                key={key}
                active={key===activeTabKey}
                {...(!['monaco', 'plSql', 'tSql'].includes(paneType) && {
                  ...{ style: { flex: 1 } },
                })}
                className={styles.queryPaneWrapper}
              >
                <ErrorBoundary>
                  {getPane()}
                </ErrorBoundary>
              </LazyWrapper>
            )
          })
        }
        <LazyWrapper active={isMonaco || isPlSql || isTsql || !!createMonacoPane?.[activeTabKey] || isCreateFnAndProcedure} style={{ flex: 1 }}>
          <div className={styles.monacoPaneWrapper}>
            <div className={classNames(!visibleQueryTabs && styles.hide)}>
              <ErrorBoundary>
                <MonacoPane
                  theme={theme}
                  modelCache={modelCacheRef.current}
                  isCreateView={!!createMonacoPane?.[activeTabKey] || isCreateFnAndProcedure}
                  resultPageSize={resultPageSize}
                />
              </ErrorBoundary>
            </div>
            <div className={styles.resultWrapper}>
              {editorTabs.map(({ key }) => (
                <LazyWrapper
                  className={styles.resultContent}
                  key={key}
                  active={key === activeTabKey}
                >
                  <ErrorBoundary>
                    <ResultTabs queryTabKey={key} />
                  </ErrorBoundary>
                </LazyWrapper>
              ))}
            </div>
          </div>
        </LazyWrapper>
      </div>
      {/* 标签页重命名 */}
      {renameVisible && <PaneRenameModal
        visible={renameVisible}
        onCancel={() => {
          setRenameVisible(false)
          setPaneRenameProps({})
        }}
        {...paneRenameProps}
      />}
      {/* 设置连接颜色 */}
      {connColorVisible && <ConnColorSettingModal
        visible={connColorVisible}
        onCancel={() => {
          setConnColorVisible(false)
          setConnColorTabProps({})
        }}
        {...connColorTabProps}
      />}
    </section>
  )
}