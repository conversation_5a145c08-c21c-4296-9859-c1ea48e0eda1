import { QueryPool } from 'src/components/queryPoolPane/QueryPool';

class QueryPoolManager {
  private pools: Map<string, QueryPool> = new Map();

  add(key: string, pool: QueryPool) {
    // 在添加新池之前，检查是否已存在旧池，如果存在则将其移除。
    // 这可以防止同一选项卡意外产生多个活动池。
    this.remove(key);
    this.pools.set(key, pool);
    console.log(this.pools.size, 'pool add');
  }

  get(key: string): QueryPool | undefined {
    return this.pools.get(key);
  }

  remove(key: string) {
    const pool = this.pools.get(key);
    console.log('pool remove', pool)
    if (pool) {
      // 假设池有一个 'cancel' 方法来停止任何正在进行的查询。
      // 如果 QueryPool 的实现不同，则需要进行调整。
      if (typeof (pool as any).cancel === 'function') {
        (pool as any).cancel();
      }
      this.pools.delete(key);
    }
  }
}

export const queryPoolManager = new QueryPoolManager();