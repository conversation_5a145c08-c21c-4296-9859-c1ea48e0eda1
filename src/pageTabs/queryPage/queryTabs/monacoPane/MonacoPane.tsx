import React, {
  use<PERSON><PERSON>back,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import * as _ from 'lodash'
import { queryPoolManager } from './queryPoolManager'
import { BLOCK_SIZE } from 'src/constants'
import { useDispatch, useRequest, useSelector } from 'src/hook'
import {
  editor,
  IPosition,
  IRange,
  IScrollEvent,
  ISelection,
  Uri,
  Range,
} from 'monaco-editor'
import {
  useEditorInstance,
  QueryPool,
  QueryPoolContext,
  BaseEditor,
  ChangeModelContentEvent,
  getModelLanguageId,
  Iconfont,
} from 'src/components'
import Watermark from '@pansy/react-watermark'
import { MonacoToolbar } from './monacoToolbar'
import { customizeLanguage } from './customizeLanguage'
import {
  activeMonacoPaneInfoSelector,
  DatabaseInfo,
  executeEditorSql,
  explainEditorSql,
  PaneInfo,
  setTabExecutionStatus,
  updateMonaco,
  updatePaneInfoAboutErrormark,
  saveExecuteActiveTabParams,
  saveExecuteActiveTabInfo,
  setTabExecutionStatusPercentage,
  addPane,
  updateTabsInfo
} from 'src/pageTabs/queryPage/queryTabs/queryTabsSlice'
import {
  setErrorJump,
  setIsRun,
  setLastErrorPosition,
} from 'src/pageTabs/queryPage/resultTabs/resultTabsSlice'
import {
  calculateErrorPositionInFullText,
  getOperatingObject,
  getSqlToExecute,
  TransactSqlSplitter,
} from 'src/util'
import { ResizableBox } from 'react-resizable'
import {
  executeSqlSegment, getDataSourceDescription,
  getConnectionInfoFromSqlSegment,
  getConnExecSqlRowNum,
  getShortcutList,
  getUserConfig
} from 'src/api'
import {
  resetResultList,
  resetSegmentMsg,
  setDeferExecuteResultMapping
} from '../../resultTabs/resultTabsSlice'
import { setHotKeys } from 'src/store/extraSlice/settingSlice'
import { useLanguageClientContext } from 'src/components/BaseEditor/useLSP'
import styles from './index.module.scss'
import SqlExecuteAuditeModal from "./SqlExecuteAuditeModal"
import { Alert, message } from 'antd'
import classnames from 'classnames'
import { setVisibleAlert } from 'src/appPages/login/loginSlice'
import { AUTO_COMMIT } from '../designNewViewPane/constants'
import { resetCurrentLogs } from 'src/store/extraSlice/logsSlice'

import { useTranslation } from 'react-i18next'
import { isRealSelection } from '../../utils';

interface MonacoPaneProps {
  modelCache: Map<string, editor.ITextModel>
  theme?: 'dark' | 'light'
  isCreateView?: boolean    // 创建视图\创建存储过程\创建函数
  [p: string]: any
}

export const MonacoPane: React.FC<MonacoPaneProps> = (props) => {
  const { theme = 'light', modelCache, isCreateView } = props
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const poolMap = useContext(QueryPoolContext)
  const [editorInstance] = useEditorInstance()
  const languageClient = useLanguageClientContext()
  const activePaneInfo = useSelector(activeMonacoPaneInfoSelector)
  const dataSourceDescInfo = useSelector((S) => S.dataSource.dataSourceMap)
  const userId = useSelector((state) => state.login.userInfo.userId)
  const { activeTabKey, tabInfoMap, connectionsAndUserSettingInfo } = useSelector((state) => state.queryTabs)
  const visibleAlert = useSelector((state) => state.login.visibleAlert) // 编辑器执行行数提示
  /* 获取执行错误记录 */
  const segmentMsgList = useSelector((state) => state.resultTabs.segmentMsg)
  const errorJump = useSelector((state) => state.resultTabs.errorJump)
  const isRun = useSelector((state) => state.resultTabs.isRun)
  const lastErrorPosition = useSelector((state) => state.resultTabs.lastErrorPosition)
  // watermark
  const { watermarkSetting, watermarkValue } = useSelector((state: any) => state.login.userInfo)
  const { watermarkEffect } = useSelector((state: any) => state.login)
  // 执行语句装饰标识
  const [showExecuteEffect, setShowExecuteEffect] = useState<boolean>(false)
  // 立即计算初始高度，避免时序问题
  const [paneHeight, setPaneHeight] = useState(() => {
    const height = window.innerHeight
    return height > 468 ? height - 268 : 200
  })
  const decorationsRef = useRef<any>({})
  const modelOptionRef = useRef<PaneInfo | undefined>(undefined)
  
  // 用于debounce的定时器引用
  const contentChangeTimerRef = useRef<NodeJS.Timeout | null>(null)
  const cursorChangeTimerRef = useRef<NodeJS.Timeout | null>(null)
  const scrollChangeTimerRef = useRef<NodeJS.Timeout | null>(null)
  // 随机获取一个连接对应的用户设置信息（都一样）
  const curConnectionsAndUserSettingInfo = (connectionsAndUserSettingInfo && Object.values(connectionsAndUserSettingInfo)?.[0]) || {}

  const {
    key='0',
    value,
    connectionId,
    databaseName,
    language = 'sql',
    connectionType,
    execValueRange,
    hasCommitExecError = false,
    pending,
    schemaName,
    prepared,
    autoRun,
    nodeType
  } = activePaneInfo || {}
  const { currentLogs=[] } = useSelector((state) => state.logs[key]) || {}

  const targetModel = useMemo(() => {
    if (!key) return null
    // 在 cache 中查找已有的 model, 如果没有则创建并存储 model
    let target: editor.ITextModel
    const cachedModel = modelCache.get(key)
    if (cachedModel) {
      target = cachedModel
    } else {
      target = editor.createModel(
        '',
        connectionType ? getModelLanguageId(connectionType) : 'mydsl',
        Uri.parse(`inmemory://${key}/${key}`),
      )
      modelCache.set(key, target)
    }
    return target
  }, [key, connectionType, modelCache])

  useEffect(()=>{
    // 执行sql支持装饰 且 执行日志有内容执行装饰sql
    if(showExecuteEffect && currentLogs?.length){
      handleExecDecoration()  
    }
    // 解决切换showExecuteEffect时重置已有装饰
    if(!showExecuteEffect){
      resetExecDecoration()
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  },[currentLogs, showExecuteEffect])

  useEffect(() => {
    if (!editorInstance) return
    if (activePaneInfo?.['cursorPosition']) {
      editorInstance.setPosition(activePaneInfo?.['cursorPosition'])
      editorInstance.focus()
    }
    if (activePaneInfo?.scrollTop) {
      editorInstance.setScrollTop(activePaneInfo?.scrollTop)
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTabKey])

  // 设置语句执行高亮标识
  useEffect(() => {
    const showExecuteEffect = curConnectionsAndUserSettingInfo?.userSettings?.showExecuteEffect
    setShowExecuteEffect(!!showExecuteEffect)
  }, [curConnectionsAndUserSettingInfo?.userSettings?.showExecuteEffect])
  
  // 获取个人设置中的hotKeys
  const { run: runGetShortcutList } = useRequest(getShortcutList, {
    manual: true,
    onSuccess: (res) => {
   
      if (res?.data?.length) {
        const hotkeys = res.data.reduce((pre: any, next: any) => {
          return {
            ...pre,
            [next.name]: next.key,
          }
        }, {})
        dispatch(setHotKeys({ ...hotkeys }))
      }
    },
  })

  useEffect(() => {
    runGetShortcutList({pageNum: 1, pageSize: 1000})
  }, [runGetShortcutList])

  useEffect(() => {
    // 初始化 model 内容
    if (!editorInstance || !targetModel) {
      return
    }
    if (value !== undefined && targetModel.getValue() !== value) {
      handlePushEdit(targetModel, value)
    }
    function handlePushEdit(model: editor.ITextModel, value: string): void {
      editorInstance?.executeEdits("", [
        {
          range: model.getFullModelRange(),
          text: value,
          forceMoveMarkers: true
        }
      ])
    }
  }, [value, targetModel, editorInstance])

  // 声明一个 mutable modelOptionRef 存放当前 modelOption, 避免 useCallback deps 数组引入 modelOption
  // 作用类似 class component 的实例属性, 使注册的事件触发时, 取到的是当前 modelOption 而不是闭包中的
  useEffect(() => {
    modelOptionRef.current = activePaneInfo
    return () => {
      modelOptionRef.current = undefined
    }
  }, [activePaneInfo])

  useEffect(() => {
    editor.setTheme(theme)
  }, [theme])

  useEffect(() => {
    if (!targetModel || !connectionType) return
    const modelLanguageId = getModelLanguageId(connectionType)
    let connectionContext: DatabaseInfo | undefined
    if (connectionId && connectionType) {
      connectionContext = {
        connectionId,
        connectionType,
        databaseName,
        schemaName,
      }
    }
    // register language/completion
    const disposers = customizeLanguage(
      modelLanguageId,
      dataSourceDescInfo,
      connectionContext,
    )
    editor.setModelLanguage(targetModel, language)
    return () => disposers.forEach((disposer) => disposer.dispose())
  }, [
    connectionId,
    connectionType,
    dataSourceDescInfo,
    databaseName,
    language,
    schemaName,
    targetModel,
  ])

  // model中的 文本 标红部分
  useEffect(() => {
    if (!key || _.isEmpty(lastErrorPosition)) return

    if (
      targetModel &&
      lastErrorPosition &&
      !(lastErrorPosition.startLineNumber === 0 && lastErrorPosition.endLineNumber === 0 && lastErrorPosition.startColumn === 0 && lastErrorPosition.endColumn === 0) &&
      (!hasCommitExecError || errorJump)
    ) {
      const selectRange = (activePaneInfo?.execValueRange) ? (activePaneInfo?.execValueRange) : targetModel?.getFullModelRange()
      /* 当所选范围中的sql语句，第一条sql语句前有空格行时，要进行+，因为传到后端的sql语句没有包含这些空格行，计算错误sql语句位置时会出错 */
      let leadingWhitespaceLines = 0;
      if (selectRange && !errorJump && isRun) {
        // 获取选中范围之前的文本
        const beforeText = targetModel?.getValueInRange(selectRange);

        // 以换行符分割文本为行数组
        const lines = beforeText.split('\n');

        // 计算空白行数
        for (let i = 0; i < lines.length; i++) {
          if (lines[i].trim() === '') {
            leadingWhitespaceLines++;
          } else {
            break;
          }
        }
      }
      let positionJson:
        {
          endColumn: number
          endLineNumber: number
          startColumn: number
          startLineNumber: number
        } = {
        "endColumn": 0,
        "endLineNumber": 0,
        "startColumn": 0,
        "startLineNumber": 0
      }
      // 当前执行的sql语句所在的tab的key
      const queryTabKey = activePaneInfo?.key
      if (queryTabKey) {
        if (queryTabKey in lastErrorPosition && !!lastErrorPosition[queryTabKey]) {
          positionJson = lastErrorPosition[queryTabKey]
        }
      }
      // 点击滚动定位
      if (errorJump) {
        dispatch(setErrorJump(false))
      }
      if (positionJson &&
        !(positionJson.startLineNumber === 0 &&
          positionJson.endLineNumber === 0 &&
          positionJson.startColumn === 0 &&
          positionJson.endColumn === 0)) {
        let editorExecErrorMarkRange: any =
          [new Range(positionJson.startLineNumber,
            positionJson.startColumn,
            positionJson.endLineNumber,
            positionJson.endColumn)]
        // 执行过后，所选范围全为 全局, 更新 json 中的position为相对于 全局范围,而非所选sql语句的范围
        // 而切换 tab、点击滚动定位，则不需要再重新进行位置的计算
        if (isRun) {
          positionJson = calculateErrorPositionInFullText(targetModel.getFullModelRange(), selectRange, positionJson)
          positionJson = {
            "startLineNumber": positionJson.startLineNumber + leadingWhitespaceLines,
            "startColumn": positionJson.startColumn,
            "endLineNumber": positionJson.endLineNumber + leadingWhitespaceLines,
            "endColumn": positionJson.endColumn,
          }
          editorExecErrorMarkRange =
            [new Range(positionJson.startLineNumber,
              positionJson.startColumn,
              positionJson.endLineNumber,
              positionJson.endColumn)]

          // 执行过后，所选范围全为 全局, 更新 json 中的position为相对于 全局范围,而非所选sql语句的范围
          if (queryTabKey) {
            if (queryTabKey in lastErrorPosition && !!lastErrorPosition[queryTabKey]) {
              let tmp = { ...lastErrorPosition }
              tmp[queryTabKey] = positionJson
              dispatch(
                setLastErrorPosition(tmp)
              )
            }
          }
        } else {
          dispatch(setErrorJump(false))
        }
        // 标红
        if (editorExecErrorMarkRange[0] && editorInstance) {
          // 标记失败装饰
          commitErrorMark(editorInstance, targetModel, editorExecErrorMarkRange, showExecuteEffect)
          dispatch(
            updatePaneInfoAboutErrormark({
              execValueRange,
              hasCommitExecError: true,
              editorExecErrorMarkRange,
            }),
          )
        }
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    activePaneInfo,
    dispatch,
    editorInstance,
    execValueRange,
    hasCommitExecError,
    key,
    pending,
    segmentMsgList,
    targetModel,
    errorJump,
    lastErrorPosition,
    isRun
  ])

  useEffect(() => {
    if (
      !targetModel ||
      !connectionType ||
      !languageClient ||
      !languageClient.enhancedApi?.didChangeLanguageContext
    ){
      return
    }
    const modelLanguageId = getModelLanguageId(connectionType)
    editor.setModelLanguage(targetModel, modelLanguageId)
    languageClient.enhancedApi?.didChangeLanguageContext({
      connectionId: connectionId || '',
      dataSourceType: connectionType,
      languageType: connectionType,
      operatingDatabase: databaseName,
      operatingSchema: schemaName,
      userId,
    } as any)
  }, [
    connectionId,
    connectionType,
    databaseName,
    languageClient,
    schemaName,
    targetModel,
    userId,
  ])

  // sdt 打开表，在切库完成后自动执行语句
  useEffect(() => {
    if (key && editorInstance && autoRun && prepared) {
      dispatch(updateMonaco({ key, autoRun: false }))
      //等待窗口初始化，延迟执行，窗口初始化的时候会更改状态，如果直接调用执行会出现状态变化的顺序不对
      setTimeout(() => {
        //打开表、打开视图 使用执行逻辑CQ-7807
        if (nodeType && ['view', 'table'].includes(nodeType)) {
          handleExecuteSql()
        }else {
          dispatch(executeEditorSql(t, getSqlToExecute(editorInstance, !showExecuteEffect)))
        }
      }, 500);
    }
  }, [autoRun, dispatch, editorInstance, key, prepared, showExecuteEffect, nodeType])

  // 数据库描述
  const { data: dataSourceDescription, run: fetchDescription } = useRequest(
    getDataSourceDescription,
    { manual: true },
  )

  useEffect(() => {
    if (connectionId && connectionType) {
      fetchDescription(connectionType)
    }
  }, [connectionId, connectionType, fetchDescription])

  useEffect(() => {
    // 改变model时重置 errormark
    clearErrorMask()
    resetExecDecoration()
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dispatch, targetModel])

  // 动态设置面板高度
  const queryPaneHeight = useCallback(() => {
    const height = window.innerHeight
    const newPaneHeight = height > 468 ? height - 268 : 200
    setPaneHeight(newPaneHeight)
  }, [])

  // 监听页面大小变化 - 使用useEffect管理事件监听器
  useEffect(() => {
    window.addEventListener('resize', queryPaneHeight, { passive: true })
    
    return () => {
      window.removeEventListener('resize', queryPaneHeight)
    }
  }, [queryPaneHeight])

  //  清除错误信息的方法
  const clearErrorMask = useCallback(() => {
    dispatch(setLastErrorPosition({}))
    dispatch(updatePaneInfoAboutErrormark({}))
    if (targetModel) {
      clearModelDecorations(targetModel)
    }
  }, [dispatch, targetModel])

  // 清除执行sql装饰效果
  const resetExecDecoration = useCallback(() => {
    const currentDerationsLength = decorationsRef.current?.[key]?.length
    if(!currentDerationsLength){
      return
    }
    const range = targetModel?.getDecorationRange(
      decorationsRef.current[key][currentDerationsLength - 1],
    )
    range && editorInstance?.revealRangeInCenterIfOutsideViewport(range)
    const clearDecorationValue = decorationsRef.current?.[key]?.map((item: any)=>item?.value)?.flat()
    targetModel?.deltaDecorations(clearDecorationValue, [])
    decorationsRef.current[key] = []
  }, [key, targetModel, editorInstance])

  /** 编辑器更改 model 内容的事件处理函数 */
  const handleChangeModelContent = useCallback((editor: any) => {
    // 清理之前的定时器
    if (contentChangeTimerRef.current) {
      clearTimeout(contentChangeTimerRef.current)
    }
    
    contentChangeTimerRef.current = setTimeout(() => {
      if (modelOptionRef.current) {
        const { key } = modelOptionRef.current
        const newValue = editor.getValue()
        
        // 只在值真正变化时才更新 Redux
        if (value !== newValue) {
          dispatch(updateMonaco({ key, value: newValue }))
          clearErrorMask()
          resetExecDecoration()
        }
      }
      contentChangeTimerRef.current = null
    }, 150) // 减少延迟时间到150ms
  }, [dispatch, value, clearErrorMask, resetExecDecoration])

  const handleChangeCursorPosition = useCallback((position: IPosition) => {
    // 清理之前的定时器
    if (cursorChangeTimerRef.current) {
      clearTimeout(cursorChangeTimerRef.current)
    }
    
    cursorChangeTimerRef.current = setTimeout(() => {
      if (modelOptionRef.current) {
        const { key } = modelOptionRef.current
        const cursorPosition = { ...position }
        
        // 只在位置真正变化时才更新 Redux
        const currentPosition = activePaneInfo?.cursorPosition
        if (!currentPosition || 
            currentPosition.lineNumber !== position.lineNumber || 
            currentPosition.column !== position.column) {
          dispatch(updateMonaco({ key, cursorPosition }))
        }
      }
      cursorChangeTimerRef.current = null
    }, 200) // 光标位置更新可以稍快一些
  }, [dispatch, activePaneInfo?.cursorPosition])

  const handleChangeScrollTop = useCallback((e: IScrollEvent) => {
    // 清理之前的定时器
    if (scrollChangeTimerRef.current) {
      clearTimeout(scrollChangeTimerRef.current)
    }
    
    scrollChangeTimerRef.current = setTimeout(() => {
      if (modelOptionRef.current) {
        const { key } = modelOptionRef.current
        
        // 只在滚动位置明显变化时才更新 Redux (避免微小变化)
        const currentScrollTop = activePaneInfo?.scrollTop || 0
        const scrollDiff = Math.abs((e.scrollTop || 0) - currentScrollTop)
        
        if (scrollDiff > 5) { // 5px 的滚动差异阈值
          dispatch(updateMonaco({ key, scrollTop: e.scrollTop }))
        }
      }
      scrollChangeTimerRef.current = null
    }, 250) // 滚动更新可以稍慢一些
  }, [dispatch, activePaneInfo?.scrollTop])

  // 清理所有定时器的 effect
  useEffect(() => {
    return () => {
      // 组件卸载时清理所有定时器
      if (contentChangeTimerRef.current) {
        clearTimeout(contentChangeTimerRef.current)
        contentChangeTimerRef.current = null
      }
      if (cursorChangeTimerRef.current) {
        clearTimeout(cursorChangeTimerRef.current)
        cursorChangeTimerRef.current = null
      }
      if (scrollChangeTimerRef.current) {
        clearTimeout(scrollChangeTimerRef.current)
        scrollChangeTimerRef.current = null
      }
    }
  }, [])

  const { run: execSegment } = useRequest(executeSqlSegment, {
    manual: true,
    onSuccess: (data = {}) => {
     
      const { messageId } = data ?? {};

      messageId && dispatch(saveExecuteActiveTabInfo({ key: messageId, data }))
      messageId && dispatch(setTabExecutionStatusPercentage({ key: messageId, executePercentage: data.executePercentage, executeStatusMessage: data.executeStatusMessage }));
      //存储deferExecute执行结果
      messageId && dispatch(setDeferExecuteResultMapping({ key: messageId, data }))
    },
    /* 当接口报错时，需手动结算执行状态 */
    onError: (e) => {
      dispatch(setTabExecutionStatus({ key: activePaneInfo?.key as string, pending: false }))
      dispatch(setTabExecutionStatusPercentage({ key: activePaneInfo?.key as string, executePercentage: 100, executeStatusMessage: `${t('sdo_execution_completed')}` }));
    },
  })

  /* 执行前重置错误提示 */
  const updateErrormarkInfo = useCallback(
    (selection?: ISelection | null) => {
      let execValueRange = undefined
      if (selection && isRealSelection(selection)) {
        const {
          selectionStartLineNumber,
          selectionStartColumn,
          positionLineNumber,
          positionColumn,
        } = selection
        execValueRange = {
          startColumn: selectionStartColumn,
          endColumn: positionColumn,
          startLineNumber: selectionStartLineNumber,
          endLineNumber: positionLineNumber,
        }
      }
      dispatch(
        updatePaneInfoAboutErrormark({
          execValueRange: execValueRange,
          hasCommitExecError: false,
          editorExecErrorMark: [],
        }),
      )
    },
    [dispatch],
  )

  /** 执行 sql 语句的事件处理函数 */
  /** flashbackSql是为了传入闪回语句使用，如果传入了就是优先级较高 */
  const handleExecuteSql = useCallback(async (flashbackSql?: string) => {
    const modelOption = modelOptionRef.current
    if (!modelOption || !editorInstance) return
    const { connectionId, connectionType, databaseName, key, plSql, tSql } =
      modelOption
    const sqlToExecute = flashbackSql || getSqlToExecute(editorInstance, !showExecuteEffect)
    // 缺少执行的必须条件（上下文和语句）
    if (!connectionId || !connectionType || !sqlToExecute) return
    // 在开启查询后 去除上一次的错误提示
    clearErrorMask()
   
    // 重置错误提示
    const selection = editorInstance.getSelection()
    const model = editorInstance.getModel()

    const fullSelection = model?.getFullModelRange()
    
    // 根据是否有真实选择决定使用哪个对象的参数
    let effectiveSelection: IRange | null = null
    if (isRealSelection(selection)) {
      // 从 Selection 对象中提取四个参数
      effectiveSelection = {
        startLineNumber: selection?.startLineNumber || 1,
        startColumn: selection?.startColumn || 1,
        endLineNumber: selection?.endLineNumber || 1,
        endColumn: selection?.endColumn || 1
      }
    } else {
      // 直接使用 Range 对象
      effectiveSelection = fullSelection || null
    }

    updateErrormarkInfo(selection)
    // 执行sql支持装饰,先重置已有装饰
    if(showExecuteEffect){
      resetExecDecoration()
      dispatch(resetCurrentLogs(key))
    }
    // 重置结果信息和结果集
    dispatch(resetSegmentMsg(key))
    dispatch(resetResultList(key))
    // 开启一轮新查询
    dispatch(setTabExecutionStatus({ key, pending: true }))
    //重置执行状态
    dispatch(setTabExecutionStatusPercentage({key: key, executePercentage: 0, executeStatusMessage: `${t('sdo_executing_status')}...0%`}));

 
    //获取当前标签页的分页大小设置
    const curTabInfo = tabInfoMap?.[activeTabKey] || {};
    
    // 优先使用已保存的 resultPageSize，如果没有则使用连接设置的默认值，最后使用 100 作为兜底
    let pageSize = curConnectionsAndUserSettingInfo?.connectionExecSqlRowNum || curTabInfo?.resultPageSize || 100;
    let rowNum = pageSize;
    
    if (tabInfoMap?.[activeTabKey]?.hasOwnProperty('isScrollLoading')) {
      //滚动加载方式 重新执行的时候会去拿最新连接设置的分页条数
      if (tabInfoMap?.[activeTabKey]?.isScrollLoading) {
        rowNum = curConnectionsAndUserSettingInfo?.connectionExecSqlRowNum
        await dispatch(updateTabsInfo({ ...curTabInfo, resultPageSize: rowNum }))
      }else if (!curTabInfo?.resultPageSize) {
          // 翻页模式 不需要获取最新的连接设置分页条数 自己有页码选择设置
          rowNum = pageSize + 1;
          await dispatch(updateTabsInfo({ ...curTabInfo, resultPageSize: rowNum, settingPageSize: rowNum }))
       }else {
          rowNum = pageSize + 1;
          await dispatch(updateTabsInfo({ ...curTabInfo, resultPageSize: rowNum - 1, settingPageSize: rowNum }))
      }
    }else {
      //可能下拉选择连接信息 默认是滚动模式
      rowNum = curConnectionsAndUserSettingInfo?.connectionExecSqlRowNum
      await dispatch(updateTabsInfo({ ...curTabInfo, resultPageSize: rowNum, isScrollLoading: true }))
    }

    // 进入query pool 逻辑
    const pool = new QueryPool(
      (params) => {
        return execSegment({
          offset: 0,
          rowCount: rowNum,
          selection: effectiveSelection,
          ...params,
        })
      },
      {
        connectionId,
        dataSourceType: connectionType,
        operatingObject: getOperatingObject(
          { databaseName, schemaName },
          connectionType,
        ),
        databaseName,
        tabKey: key,
        plSql,
        tSql,
        autoCommit: activePaneInfo?.txMode === 'auto' || 
          (tabInfoMap?.[activeTabKey]?.paneType === "obCreateView" && AUTO_COMMIT.includes(connectionType?.toLocaleLowerCase())),
        isFlashbackAction: flashbackSql ? true : undefined,
      },
    )
    pool.query(sqlToExecute)
    poolMap?.set(key, pool)


    // 使用新的管理器来存储查询池
    queryPoolManager.add(key, pool);
    //存储执行语句参数 申请命令复核需要这些参数
    dispatch(saveExecuteActiveTabParams({
      connectionId,
      dataSourceType: connectionType,
      operatingObject: getOperatingObject(
        { databaseName, schemaName },
        connectionType,
      ),
      databaseName,
      tabKey: key,
      plSql,
      tSql,
      offset: 0,
      rowCount: BLOCK_SIZE,
      statements: pool.getQuerysegments(),
      autoCommit: activePaneInfo?.txMode === 'auto',
    }))
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    dispatch,
    editorInstance,
    execSegment,
    poolMap,
    schemaName,
    updateErrormarkInfo,
    activePaneInfo?.txMode,
    showExecuteEffect,
    activeTabKey,
    JSON.stringify(tabInfoMap?.[activeTabKey])
  ])


  // 执行sql装饰效果
  const handleExecDecoration = () => {
    const index = currentLogs?.length > 0 ? currentLogs?.length - 1 : 0
    for(let i=0; i<=index; i++){
      // 没有返回错误语句位置信息时清空所有装饰
      if (!currentLogs[i]?.position) {
        resetExecDecoration()
        return
      }
      // 结果集编辑或其他非sql执行的操作日志不渲染装装
      if (!currentLogs[i]?.executeSqlFlag) {
        return
      }
      const {
        startLine: startLineNumber = 0, 
        startCol: startColumn = 0, 
        stopLine: endLineNumber = 0,
        stopCol: endColumn = 0
      } = currentLogs[i]?.position || {}
      let realStartLineNumber = startLineNumber;
      let realEndLineNumber = endLineNumber;
      let needAddLineNumber = 0;
      const selection = editorInstance?.getSelection()
      // 有选中执行时需要计算实际行数
      if (selection && isRealSelection(selection)) {
        const {
          selectionStartLineNumber,
          positionLineNumber,
        } = selection
        needAddLineNumber = selectionStartLineNumber
        if(selectionStartLineNumber > positionLineNumber){
          needAddLineNumber = positionLineNumber
        }
        realStartLineNumber = needAddLineNumber + startLineNumber - 1
        realEndLineNumber =  needAddLineNumber + endLineNumber - 1
      }
      const execBarRange: IRange = {
        startLineNumber: realStartLineNumber,
        startColumn,
        endLineNumber: realStartLineNumber,
        endColumn: startColumn,
      }
      const execRange = {
        startLineNumber: realStartLineNumber,
        startColumn,
        endLineNumber: realEndLineNumber,
        endColumn
      }
      const execBarRangeKey = execBarRange?.startLineNumber + '_' + execBarRange?.endLineNumber
      const hasDecoration: any[] = decorationsRef.current[key]?.filter((item: any) => item?.execBarRangeKey === execBarRangeKey)
      // 语句执行时,发现同一行已经存在装饰
      if(hasDecoration?.length){
        // 语句执行成功时,停止成功装饰(若之前已成功不需要再次装饰,若失败应保持失败装饰,失败优先)
        if(currentLogs[i]?.success){
          continue 
        }
        // 语句执行失败,若已有失败装饰不需再次重复装饰
        if(!hasDecoration[0]?.status){
          continue
        }
        // 语句执行失败,若已有成功装饰则删除已有成功装饰
        targetModel?.deltaDecorations(hasDecoration?.[0]?.value, [])
        decorationsRef.current[key] = decorationsRef.current[key]?.filter((item: any) => item?.execBarRangeKey !== execBarRangeKey)
      }
     
      // 默认正确语句
      let des: any[] = [
        { range: execBarRange, options: { marginClassName: 'execSuccessBar' } },
      ]
      
      // 根据接口返回的错误行计算错误语句具体范围 
      let errorWavyRange: IRange|null = null
      const errorWavyLine = currentLogs[i]?.errorLineNumber
      if(targetModel && ![undefined, null].includes(errorWavyLine)){
        // 计算实际行数
        let realErrorWavyLine = needAddLineNumber ? errorWavyLine + needAddLineNumber - 1 : errorWavyLine;
        try {
          const wavyStartColumn = targetModel?.getLineFirstNonWhitespaceColumn(realErrorWavyLine)
          const wavyEndColumn = targetModel?.getLineLastNonWhitespaceColumn(realErrorWavyLine);
          let realWavyStartColumn = wavyStartColumn
          let realWavyEndColumn = wavyEndColumn
          // 错误行和position.startLine一致，使用position的startColumn
          if(currentLogs[i]?.errorLineNumber === currentLogs[i]?.position?.startLine && wavyStartColumn < startColumn ){
            realWavyStartColumn = startColumn
          }
          // 错误行和position.stopLine一致，使用position的endColumn
          if(currentLogs[i]?.errorLineNumber === currentLogs[i]?.position?.stopLine && wavyEndColumn > endColumn){
            realWavyEndColumn = endColumn
          }
          
          errorWavyRange = {
            startLineNumber: realErrorWavyLine,
            startColumn: realWavyStartColumn,
            endLineNumber: realErrorWavyLine,
            endColumn: realWavyEndColumn,
          }
        } catch (error) {
          console.log('errorWavyRange-error---', error)
        }
      }
      
      if(!currentLogs[i]?.success){
        // 错误语句
        des = [
          { range: execBarRange, options: { marginClassName: 'execErrorBar' } },
          { range: execRange, options: { inlineClassName: 'editor-exec-error-markline' } },
        ]
        if(errorWavyRange){
          des.push({
            range: errorWavyRange,
            options: {
              inlineClassName: 'underline-wavy-decorations',
            },
          })
        }
      }
      const newDecoration = targetModel?.deltaDecorations(
        [], 
        [...des],
      )

      if(newDecoration?.length){
        const decorationItem = {
          execBarRangeKey,
          status: currentLogs[i]?.success,
          value: newDecoration
        }
        decorationsRef.current[key]?.length 
        ? decorationsRef.current[key].push({...decorationItem})
        : decorationsRef.current[key] = [{...decorationItem}]
      }
    }
  }

  /** 执行计划 */
  const handleExplainSql = useCallback(() => {
    if (!editorInstance) return
    // 重置sql装饰
    resetExecDecoration()
    const text = getSqlToExecute(editorInstance, !showExecuteEffect)
    dispatch(explainEditorSql(t, text))
  }, [dispatch, editorInstance, showExecuteEffect, t, resetExecDecoration])

  //查看表结构
  const getViewTableStructure = async () => {
    const modelOption = modelOptionRef.current
    if (!modelOption || !editorInstance) return
    const { connectionId, connectionType, databaseName } = modelOption;
    const position: any = editorInstance.getPosition();
    const sqlToExecute = getSqlToExecute(editorInstance, !showExecuteEffect)
    //查看表结构 index 光标在整个文本中位置
    const index = position && editorInstance.getModel()?.getOffsetAt(position)
    const lineCount = editorInstance?.getModel()?.getLineCount() || 1;
    const curLineNumber = position?.lineNumber || 1;
    //当前行语句
    const sql = editorInstance?.getModel()?.getLineContent(curLineNumber);
    if (sql?.trim() !== "" && lineCount >= curLineNumber) {
      try {
        const connectionInfo = await getConnectionInfoFromSqlSegment({
          connectionId: connectionId || null,
          dataSourceType: connectionType || null,
          databaseName,
          schemaName,
          //@ts-ignore
          operatingObject: getOperatingObject({ databaseName, schemaName }, connectionType),
          charPositionInText: index - 1,
          line: position?.lineNumber || 1,
          column: position?.column || 1,
          text: sqlToExecute
        })
        if (connectionInfo?.findDefinition) {
          onOpenViewTablleStructrePanel(connectionInfo)
          return
        }
      } catch (error) {
      }
    }
    message.info(t('sdo_definition_not_found'))
  }

  const onOpenViewTablleStructrePanel = (connectionInfo: any) => {
    dispatch(
      addPane({
        tabName: connectionInfo?.tableName,
        paneType: 'viewTableStructure',
        connectionId: connectionInfo?.connectionId,
        connectionType: connectionInfo?.connectionType,
        databaseName: connectionInfo?.databaseName,
        nodePath: connectionInfo?.nodePath,
        nodeName: connectionInfo?.tableName,
        nodeType: 'table',
      }),
    )
  }

  const onPreCommit = () => {
    if (!editorInstance || !targetModel) return
    const sql = targetModel.getValue()
    const tSplitter = new TransactSqlSplitter(sql)
    tSplitter.split()
    const blocks = tSplitter.getBlocks()
    const range: IRange[] = blocks.map(({ start, end }) => {
      const startPosition = targetModel.getPositionAt(start)
      const endPosition = targetModel.getPositionAt(end)
      return {
        startLineNumber: startPosition.lineNumber,
        startColumn: startPosition.column,
        endLineNumber: endPosition.lineNumber,
        endColumn: endPosition.column,
      }
    })
    highlightBlocks(editorInstance, targetModel, range)
  }

  const clearModelDecWithSelect = () => clearModelDecorations(targetModel)

  // 处理拖拽开始事件 - 禁用全局文本选择
  const handleResizeStart = useCallback(() => {
    document.body.style.userSelect = 'none';
  }, []);

  // 处理拖拽结束事件 - 恢复全局文本选择
  const handleResizeStop = useCallback(() => {
    document.body.style.userSelect = '';
  }, []);

  // resize
  const ResizeHandle = (
    <div className={styles.resizeHandle}>
      <Iconfont type="icon-handle-8-horizontal"></Iconfont>
    </div>
  )

  return (
    <section className={styles.queryEditor}>
      <MonacoToolbar
        onPressExecute={(flashbackSql) => handleExecuteSql(flashbackSql)}
        clearModelDec={clearModelDecWithSelect}
        onPreCommit={onPreCommit}
        supportTransaction={dataSourceDescription?.supportTransaction}
        oneLibraryAndOneConnection={dataSourceDescription?.oneLibraryAndOneConnection}
        isCreateView={isCreateView}
        showExecuteEffect={showExecuteEffect}
        resetExecDecoration={resetExecDecoration}
      />
      {
        (!isCreateView && visibleAlert)
          ? <Alert
            message={t('sdo_editor_txt')}
            type='warning'
            closable
            onClose={() => { dispatch(setVisibleAlert(false)) }}
            closeText={t('sdo_close')}
            className={classnames(theme === 'dark' ? 'darkBg' : 'lightBg', 'editorContainerAlert')}
          />
          : null
      }
      {/* https://github.com/STRML/react-resizable/issues/69 */}
      <ResizableBox
        handle={ResizeHandle}
        height={Math.min(192, paneHeight)}
        width={Infinity}
        minConstraints={[Infinity, 48]}
        maxConstraints={[Infinity, paneHeight]}
        onResizeStart={handleResizeStart}
        onResizeStop={handleResizeStop}
      >
        <> 
          <Watermark
            text={watermarkValue?.length ? watermarkValue?.join(' ') : ''}
            zIndex={99}
            rotate={20}
            pack={false}
            visible={watermarkSetting}
            {...watermarkEffect}
          />
          <BaseEditor
            className={styles.editorContainer}
            model={targetModel}
            onExecuteSelectedText={handleExecuteSql}
            onChangeModelContent={handleChangeModelContent}
            onChangeCursorPosition={handleChangeCursorPosition}
            handleChangeScrollTop={handleChangeScrollTop}
            onCommandExplain={handleExplainSql}
            getViewTableStructure={getViewTableStructure}
          />
        </>
      </ResizableBox>
      {/* sql审核结果弹框内容 */}
      {
        key &&
        <SqlExecuteAuditeModal
          activePaneKey={key}
          execSegment={execSegment}
        />
      }
    </section>
  )
}

/* commit error mark */
const commitErrorMark = (
  editorInstance: editor.IStandaloneCodeEditor | null,
  model: editor.ITextModel,
  editorExecErrorMarkRange: IRange[],
  showExecuteEffect?: boolean
) => {
  if (!editorInstance || !editorExecErrorMarkRange[0]) return
  const waitCommitRange = editorExecErrorMarkRange[0]
  /* 
   * 错误语句标红
   * 带有对勾和错误icon的装饰标记在handleExecDecoration方法内已完成,所以此处不需要重新装饰,只需要保留其他逻辑
  */
  if(!showExecuteEffect){
    const execErrorMarks = editorExecErrorMarkRange.map(
      (range: IRange): editor.IModelDeltaDecoration => ({
        range: range,
        options: {
          inlineClassName: 'editor-exec-error-markline',
        },
      })
    )
    model.deltaDecorations([], execErrorMarks )
  }
  
  /* 1 滚动到对应编辑,距离顶部两行 */
  const { startLineNumber } = waitCommitRange
  const scrollPositon = editorInstance.getScrolledVisiblePosition({
    lineNumber: startLineNumber > 2 ? startLineNumber - 2 : startLineNumber,
    column: 0,
  })
  editorInstance.setScrollPosition({
    scrollTop: startLineNumber === 0 ? scrollPositon?.top : (scrollPositon?.top ? scrollPositon?.top : 0) + editorInstance.getScrollTop(),
    scrollLeft: 0,
  })
}

/* model 清空标记 */
const clearModelDecorations = (model: editor.ITextModel | null) => {
  if (!model) return
  // 若 model 已经 dispose 保护性退出 不执行getAllDecorations 防止报错
  if (model.isDisposed()) return
  const currDecotation = model.getAllDecorations()
  if (!currDecotation || !currDecotation[0]) return
  const currDecotationIdList = currDecotation
    .filter((it) =>
      ['editor-highlight', 'editor-exec-error-markline'].includes( it?.options?.inlineClassName ?? '') 
      || ['execErrorBar'].includes(it?.options?.marginClassName ?? '') 
    )
    ?.map((it) => it.id)
  model.deltaDecorations(currDecotationIdList, [])
}

/* TSQL 分块高亮 */
const highlightBlocks = (
  editorInstance: editor.IStandaloneCodeEditor | null,
  model: editor.ITextModel,
  blocksRange: IRange[],
) => {
  if (!editorInstance || !blocksRange[0]) return
  model.deltaDecorations(
    [],
    blocksRange.map(
      (range: IRange): editor.IModelDeltaDecoration => ({
        range: range,
        options: {
          inlineClassName: 'editor-highlight',
        },
      }),
    ),
  )
}