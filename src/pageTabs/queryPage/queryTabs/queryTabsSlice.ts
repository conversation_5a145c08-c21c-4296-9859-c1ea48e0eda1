import { message } from 'antd'
import { queryPoolManager } from './monacoPane/queryPoolManager'
import {
  createAsyncThunk,
  createSlice,
  PayloadAction,
  createSelector,
} from '@reduxjs/toolkit'
import { editor, IPosition, IRange } from 'monaco-editor'
import {
  DataSourceType,
  FavoriteEntity,
  getTxModeOfConnection,
  SdtNodeType,
  TxModeContext,
  UserFile,
  StatementExecuteParams,
  queryConnectionsAndUserSettings,
  getUserConfig
} from 'src/api'
import { AppThunk, RootState } from 'src/store'
import {
  BLOCK_SIZE,
  Encoding,
  EOL_SEQUENCE_TYPE,
  TSQL_BLOCK_SIZE,
} from 'src/constants'
import {SqlSplitter, getOperatingObject, TransactSqlSplitter} from 'src/util'
import {
  queryExecuteResults,
  fetchExplain,
} from '../resultTabs/resultTabsSlice'
import { v4 as uuidv4 } from 'uuid'
import { formatTabName } from './util';
import i18next from 'i18next'

interface QueryTabsState {
  activeTabKey: string
  tabKeyList: string[]
  tabInfoMap: { [key: string]: TabInfo }
  paneInfoMap: { [key: string]: PaneInfo }
  lastSavedFavorite: FavoriteEntity | null
  executeActiveTabParams: {[key: string]: string | boolean},
  executeActiveTabInfo: {[key: string]: string}, // 只存储 messageId 字符串
  tabExecutionStatusPercentageMap : { [key: string]: TabExecutionStatusPercentage },
  examineReviewResult?: {queryTabKey: string; reviewResult: any} | null // sql审核结果
  sdtBasicSettingData: any,  // sdt右键导出 安全设置-基础设置-导出设置
  newSdtSelectedKeysVal: string, // 设计视图，定位到新视图
  connectionsAndUserSettingInfo: any,  // sdt schema层右键打开表相关信息(和连接绑定的user信息)
}

interface TabExecutionStatusPercentage {
  key: string
  executePercentage: number
  executeStatusMessage: string
}

interface TabInfo {
  key: string
  /** tabPane content type */
  paneType: PaneType
  tabName?: string
  resultPageSize?: number; //当前tab窗口连接设置分页条数
  settingPageSize?: number;
  tabColor?:string // tab颜色
  [p: string]: any
}
export type PaneType =
  | 'terminal'
  | 'monaco'
  | 'plSql'
  | 'tSql'
  | 'addTable'
  | 'designTable' /** 设计表 */
  | 'viewTableStructure' /** 查看表结构 */
  | 'grid'
  | 'addView'
  | 'compileInvalidObjects' /** 编译无效对象 */
  | 'viewViewStructureMenu' /** 查看视图结构 */
  | 'obCreateView' /** ob创建视图 */
  | 'designView'  /** 设计视图 */
  | 'createFnAndProcedure' /** ob创建函数\存储过程 */
  | 'openTab'  //打开schema及以下组层级信息
  | 'viewBasicInfo'  //打开redis数据库的基本信息
  | 'diff'  // 版本对比

export interface PaneInfo
  extends Partial<MonacoEntity>,
  Partial<TerminalEntity>,
  Partial<AddTableEntity> {
  key: string
  [p: string]: any
}

interface MonacoEntity extends DatabaseInfo {
  key: string
  value: string
  language: string
  cursorPosition: IPosition
  encoding?: Encoding
  endOfLine?: EOL_SEQUENCE_TYPE
  serverFile: UserFile | null
  plSql?: boolean
  tSql?: boolean
  /* 执行在编辑器内选中Range */
  execValueRange?: IRange
  /* 本次执行过程中已经将错误 commit 到editor */
  hasCommitExecError?: boolean
  /* 错误标记 */
  editorExecErrorMark?: IRange[]
  editorExecErrorMarkRange?: any
  /** 是否需要在执行环境建立后立即执行 */
  autoRun?: boolean
  /** 执行环境准备中（当前指切库） */
  prepared?: boolean
  /** 查询窗口当前物理连接事物模式 */
  txMode?: 'auto' | 'manual'
  /** 当前窗口editor 滚动条位置 */
  scrollTop?: number
  /** 历史遗留问题，上述的cursorPosition 在原先的基础上做了节流，新的这个为实时position*/
  viewState?: editor.ICodeEditorViewState
  /** 当前pane是什么类型，通过右键点击进来的类型 */
  menuType?: string
}
interface TerminalEntity {
  key: string
  url: string
}
export interface AddTableEntity extends DatabaseInfo {
  key: string
}

// todo: 待明确意义, 应该指的是 tab 上当前物理连接的执行上下文(包括执行的参数和执行的状态)
export interface DatabaseInfo {
  connectionType: DataSourceType
  connectionId: number | string
  databaseName?: string
  /* pg、polarDB、oceanbase数据库存在 schema 层 */
  schemaName?: string
  /**
   * 执行状态, 之后可能是一个 tuple ['idle','executing'...], 当前只考虑是否在 pending
   */
  pending?: boolean
  nodePath?: string
  nodePathWithType?: string
  nodeName?: string
  nodeType?: SdtNodeType
  [p: string]: any
}

export interface ExecuteActiveTabInfo {
  executeStatus: string,
  messageData: any,
  channelId: string,
  userId: string,
  messageId: string,
  groupName: string
}

interface TabInfoUpdate {
  /** 选项卡文字 */
  tabName?: string
  resultPageSize?: number;
  settingPageSize?: number; //当前连接设置分页条数
  isScrollLoading?: boolean; //分页模式
  tabColor?:string // tab颜色
  executeEvenIfError?: boolean; //遇错执行设置
}

const initialState: QueryTabsState = {
  activeTabKey: '',
  tabKeyList: [],
  tabInfoMap: {},
  paneInfoMap: {},
  lastSavedFavorite: null,
  executeActiveTabParams: {},
  executeActiveTabInfo: {},
  tabExecutionStatusPercentageMap: {},
  examineReviewResult: null,
  sdtBasicSettingData: {},
  newSdtSelectedKeysVal: '',
  connectionsAndUserSettingInfo: {}
}
/**
 * @description clear all tabs and close session
 */
export const resetQueryTabs = createAsyncThunk<
  void,
  void,
  { state: RootState }
>('queryTabs/resetQueryTabs', async (_, { getState }) => {
  // const tabKeyList = getState().queryTabs.tabKeyList
  // closeConnectionsOnTabs(tabKeyList)
})
export const resetOtherQueryTabs = createAsyncThunk<
  string,
  string,
  { state: RootState }
>('queryTabs/resetOtherQueryTabs',  
  async (targetKey) => {
  return targetKey
})
/**
 * @description close query tab
 */
export const removeQueryPane = createAsyncThunk<
  string,
  string,
  { state: RootState }
>(
  'queryTabs/removeQueryPane',
  async (targetKey) => {
    // cocurrent fetch
    // closeConnectionsOnTab(targetKey)
    queryPoolManager.remove(targetKey);
    return targetKey
  },
  {
    condition: (targetKey, { getState }) => {
      const tabKeyList = getState().queryTabs.tabKeyList
      if (!tabKeyList.includes(targetKey)) return false
    },
  },
)

/**
 * 切库
 */
// export const changeDatabase = createAsyncThunk(
//   'queryTabs/changeDatabase',
//   async (params: QueryBase) => {
//     return connectionChangeDatabase(params)
//   },
// )

/**
 * 获取事务模式
 */
export const getTxMode = createAsyncThunk(
  'queryTabs/getTxMode',
  async (params: TxModeContext) => {
    return getTxModeOfConnection(params)
  },
)

export const queryTabsSlice = createSlice({
  name: 'queryTabs',
  initialState,
  reducers: {
    setActiveTabKey(state, action: PayloadAction<string>) {
      state.activeTabKey = action.payload
    },
    addPaneWithUniqueKey(
      state,
      action: PayloadAction<
        Partial<TabInfo> & (Partial<MonacoEntity> | Partial<TerminalEntity>)
      >,
    ) {
      const { tabKeyList, tabInfoMap, paneInfoMap } = state
      const {
        key,
        tabName = 'Untitled',
        paneType = 'monaco',
        ...rest
      } = action.payload
      const uuid = key!
      const newTabName = formatTabName(tabInfoMap, tabName)
      tabInfoMap[uuid] = { key: uuid, tabName: newTabName, paneType, ...(rest?.moreActiveTabInfo || {}) }
      paneInfoMap[uuid] = { key: uuid, ...rest }
      tabKeyList.push(uuid)
      state.activeTabKey = uuid
    },
    updateTabsInfo(
      state,
      action: PayloadAction<{ key: string } & TabInfoUpdate>,
    ) {
      const { tabKeyList, tabInfoMap } = state
      const { key, ...updateTabsInfo } = action.payload
      // 更新目标不在 tabKeyList 中, 不做更新
      if (!tabKeyList.includes(key)) return
      // 更新 tabsInfo
      const updated: any = { ...tabInfoMap[key], ...updateTabsInfo }
      if (updateTabsInfo?.tabName && updateTabsInfo?.tabName !== tabInfoMap[key]?.tabName) {
        const newTabName = formatTabName(tabInfoMap, updated?.tabName)
        updated.tabName = newTabName
      }
      tabInfoMap[key] = updated
    },

    removeQueryPane: (state, action: PayloadAction<string>) => {
      // 这里的 remove 函数不会执行 会被 fullfilled 覆盖掉
      const { activeTabKey, tabKeyList, tabInfoMap, paneInfoMap, executeActiveTabInfo, tabExecutionStatusPercentageMap,  } = state
      const targetKey = action.payload
      // 目标 key 不在 list 中, 直接 return
      if (!tabKeyList.includes(targetKey)) return
      if (targetKey === activeTabKey) {
        const index = tabKeyList.findIndex((key) => key === targetKey)
        state.activeTabKey =
          tabKeyList[index + 1] || tabKeyList[index - 1] || '-1'
      }
      // 分别删除 tabKeyList/tabInfoMap/paneInfoMap 中与 targetKey 有关的项
      state.tabKeyList = tabKeyList.filter((key) => key !== targetKey)
      // closeConnectionsOnTab(targetKey)
      delete tabInfoMap[targetKey]
      delete paneInfoMap[targetKey]
      delete executeActiveTabInfo[targetKey]
      tabExecutionStatusPercentageMap && delete tabExecutionStatusPercentageMap[targetKey]
    },

    updatePaneInfo(
      state,
      action: PayloadAction<{ key: string; paneInfo: Partial<PaneInfo> }>,
    ) {
      const { paneInfoMap } = state
      const { key } = action.payload
      paneInfoMap[key] = { ...paneInfoMap[key], ...action.payload.paneInfo }
    },

    setPrepared(
      state,
      action: PayloadAction<{ key: string; prepared: Partial<boolean> }>,
    ) {
      const { paneInfoMap } = state
      const { key } = action.payload
      paneInfoMap[key].prepared = action.payload.prepared
    },

    updateMonaco(
      state,
      action: PayloadAction<{ key: string } & Partial<MonacoEntity>>,
    ) {
      const { paneInfoMap } = state
      const { key, ...rest } = action.payload
      paneInfoMap[key] = {
        ...(paneInfoMap[key] as { key: string } & Partial<MonacoEntity>),
        ...(rest as Partial<MonacoEntity>),
      }
    },
    pushMonacoValue(
      state,
      action: PayloadAction<{ key: string; tailText: any }>,
    ) {
      const { paneInfoMap } = state
      const { key, tailText } = action.payload
      const paneInfo = paneInfoMap[key] as {
        key: string
      } & Partial<MonacoEntity>
      const totalText = `${paneInfo.value || ''}${tailText}`
      paneInfoMap[key] = {
        ...paneInfo,
        value: totalText,
      }
    },
    setLastSavedFavorite: (
      state,
      action: PayloadAction<FavoriteEntity | null>,
    ) => {
      state.lastSavedFavorite = action.payload
    },

    // modify prop 'encoding' of query tab (type monaco)
    updateMonacoTabEncoding: (
      state,
      action: PayloadAction<{ encoding?: Encoding }>,
    ) => {
      const { paneInfoMap, activeTabKey } = state
      const { encoding } = action.payload
      const paneInfo = paneInfoMap[activeTabKey]
      if (!paneInfo) return
      paneInfo.encoding = encoding
    },
    updateMonacoTabEol: (
      state,
      action: PayloadAction<{ endOfLine?: EOL_SEQUENCE_TYPE }>,
    ) => {
      const { paneInfoMap, activeTabKey } = state
      const { endOfLine } = action.payload
      const paneInfo = paneInfoMap[activeTabKey]
      if (!paneInfo) return
      paneInfo.endOfLine = endOfLine
    },
    updateMonacoTabName: (state, action: PayloadAction<string | undefined>) => {
      const tabName = action.payload
      const { tabInfoMap, activeTabKey } = state
      const tabInfo = tabInfoMap[activeTabKey]
      const newTabName = formatTabName(tabInfoMap, tabName as any || 'Untitled')
      tabInfo.tabName = newTabName
    },
    updataPaneTabColor: (
      state,
      action: PayloadAction<{ key: string; tabColor: any }>,
    ) => {
      const { paneInfoMap } = state
      const { key, tabColor } = action.payload
      const paneInfo = paneInfoMap[key]
      if (!paneInfo) return
      paneInfo.tabColor = tabColor
    },
        updatePaneScrollMode: (
      state,
      action: PayloadAction<{ key: string; isScrollLoading: boolean }>,
    ) => {
      const { paneInfoMap, tabInfoMap } = state
      const { key, isScrollLoading } = action.payload
      
      // 更新 paneInfoMap
      const paneInfo = paneInfoMap[key]
      if (!paneInfo) return
      paneInfo.isScrollLoading = isScrollLoading
      
      // 同步更新 tabInfoMap
      const tabInfo = tabInfoMap[key]
      if (tabInfo) {
        tabInfo.isScrollLoading = isScrollLoading
      }
    },
    setMonacoServerFile: (state, action: PayloadAction<UserFile | null>) => {
      const serverFile = action.payload
      const { paneInfoMap, activeTabKey } = state
      const paneInfo = paneInfoMap[activeTabKey]
      paneInfo.serverFile = serverFile
    },
    // 设置 tab 执行/解释 pending 状态
    // todo: 应该把 queryTabsSlice 和 resultTabsSlice 合二为一
    setTabExecutionStatus: (
      state,
      action: PayloadAction<{ key: string; pending: boolean }>,
    ) => {
      const { paneInfoMap } = state
      const { key, pending } = action.payload
      const paneInfo = paneInfoMap[key]
      if (!paneInfo) return
      paneInfo.pending = pending
    },

    setTabExecutionStatusPercentage: (
      state,
      action: PayloadAction<{ key: string; executePercentage: number, executeStatusMessage: string }>,
    ) => {
      const { tabExecutionStatusPercentageMap } = state
      const { key, executePercentage, executeStatusMessage } = action.payload
      const target = tabExecutionStatusPercentageMap[key]
      const tabExecutionStatusPercentage  = {key: key, executePercentage: executePercentage, executeStatusMessage: executeStatusMessage}
      tabExecutionStatusPercentageMap[key] = {
        ...target,
        ...tabExecutionStatusPercentage,
      }
    },
    /**
     * update connection and database of active paneInfo
     */
    setConnectionSession: (
      state,
      action: PayloadAction<{ key: string; paneInfo: DatabaseInfo }>,
    ) => {
      const { paneInfoMap } = state
      const { key, paneInfo } = action.payload
      const targetPane = paneInfoMap[key]
      if (targetPane) {
        paneInfoMap[key] = {
          ...targetPane,
          ...paneInfo,
        }
      }
    },
    updateConnectionSession: (
      state,
      action: PayloadAction<{ key: string; paneInfo: Partial<DatabaseInfo> }>,
    ) => {
      const { paneInfoMap } = state
      const { key, paneInfo } = action.payload
      const targetPane = paneInfoMap[key]
      if (targetPane) {
        paneInfoMap[key] = {
          ...targetPane,
          ...paneInfo,
        }
      }
    },
    /* reset paneInfo 中涉及到errormark 的部分 */
    updatePaneInfoAboutErrormark: (
      state,
      action: PayloadAction<Partial<PaneInfo>>,
    ) => {
      const { activeTabKey, paneInfoMap } = state
      const paneInfo = paneInfoMap[activeTabKey]
      if (!paneInfo) return
      const {
        execValueRange = undefined,
        // editorExecErrorMark = [],
        editorExecErrorMarkRange = [],
        hasCommitExecError = false,
      } = action.payload || {}
      // paneInfo.editorExecErrorMark = editorExecErrorMark
      paneInfo.editorExecErrorMarkRange = editorExecErrorMarkRange
      paneInfo.execValueRange = execValueRange
      paneInfo.hasCommitExecError = hasCommitExecError
    },
    //当前执行语句参数
    saveExecuteActiveTabParams: (
      state,
      action: PayloadAction<Partial<StatementExecuteParams>>
    ) => {
      const { tabKey} = action.payload
      //@ts-ignore
      state.executeActiveTabParams[tabKey] = action.payload
    },
    //当前执行语句请求后返回的关键信息
    saveExecuteActiveTabInfo: (
      state,
      action: PayloadAction<{key: string, data: ExecuteActiveTabInfo}>
    ) => {
      const { key, data } = action.payload
      // 只存储 messageId
      state.executeActiveTabInfo[key] = data.messageId
    },

    setExamineReviewResult: (
      state,
      action: PayloadAction<{queryTabKey: string; reviewResult: any} | null>,
    ) => {
      state.examineReviewResult = action.payload
    },
    setSdtBasicSettingData: (state, action: PayloadAction<any>) => {
      state.sdtBasicSettingData = action.payload
    },
    setNewSdtSelectedKeysVal: (state, action: PayloadAction<string>) => {
      state.newSdtSelectedKeysVal = action.payload
    },
    setConnectionsAndUserSettingInfo: (state, action: PayloadAction<any>) => {
      state.connectionsAndUserSettingInfo = action.payload
    },
  },
  extraReducers: (builder) => {
    builder.addCase(resetQueryTabs.fulfilled, (state) => {
      state.tabKeyList = []
      state.tabInfoMap = {}
      state.paneInfoMap = {}
    })
    builder.addCase(resetOtherQueryTabs.fulfilled, (state, action) => {
      const targetKey: string = action.payload
      const { tabInfoMap, paneInfoMap, executeActiveTabInfo, tabExecutionStatusPercentageMap } = state

      state.tabKeyList = [targetKey]
      state.tabInfoMap = {[targetKey]: tabInfoMap[targetKey]}
      state.paneInfoMap = {[targetKey]: paneInfoMap[targetKey]}
      state.executeActiveTabInfo = {[targetKey]: executeActiveTabInfo[targetKey]}
      state.tabExecutionStatusPercentageMap = {[targetKey]: tabExecutionStatusPercentageMap[targetKey]}
    })
    builder.addCase(removeQueryPane.fulfilled, (state, action) => {
      const { activeTabKey, tabKeyList, tabInfoMap, paneInfoMap, executeActiveTabInfo, tabExecutionStatusPercentageMap } = state
      const targetKey = action.payload
      // 如果当前激活 tab 被删除, 跳转到下一个或上一个 tab 页
      if (targetKey === activeTabKey) {
        const index = tabKeyList.findIndex((key) => key === targetKey)
        state.activeTabKey =
          tabKeyList[index + 1] || tabKeyList[index - 1] || '-1'
      }
      // 分别删除 tabKeyList/tabInfoMap/paneInfoMap 中与 targetKey 有关的项
      state.tabKeyList = tabKeyList.filter((key) => key !== targetKey)
      delete tabInfoMap[targetKey]
      delete paneInfoMap[targetKey]
      executeActiveTabInfo && delete executeActiveTabInfo[targetKey];
      tabExecutionStatusPercentageMap && delete tabExecutionStatusPercentageMap[targetKey]
    })
    // 切库相关
    // builder.addCase(changeDatabase.pending, (state, action) => {
    //   const { tabKey } = action.meta.arg
    //   if (tabKey) {
    //     const paneInfo = state.paneInfoMap[tabKey]
    //     if (paneInfo) {
    //       paneInfo.prepared = false
    //     }
    //   }
    // })
    // builder.addCase(changeDatabase.fulfilled, (state, action) => {
    //   const { tabKey } = action.meta.arg
    //   if (tabKey) {
    //     const paneInfo = state.paneInfoMap[tabKey]
    //     if (paneInfo) {
    //       paneInfo.prepared = true
    //     }
    //   }
    // })
    // builder.addCase(changeDatabase.rejected, (state, action) => {
    //   const { tabKey } = action.meta.arg
    //   if (tabKey) {
    //     const paneInfo = state.paneInfoMap[tabKey]
    //     if (paneInfo) {
    //       paneInfo.prepared = false
    //     }
    //   }
    // })
    // 获取事务模式
    builder.addCase(getTxMode.fulfilled, (state, action) => {
      if(!action.payload) return
      const { txMode } = action.payload
      const { tabKey } = action.meta.arg
      const paneInfo = state.paneInfoMap[tabKey]
      if (paneInfo) {
        paneInfo.txMode = txMode
      }
    })
  },
})

export const {
  setActiveTabKey,
  addPaneWithUniqueKey,
  updateTabsInfo,
  updateMonaco,
  updatePaneInfo,
  setPrepared,
  setLastSavedFavorite,
  updateMonacoTabEncoding,
  updateMonacoTabEol,
  updateMonacoTabName,
  setMonacoServerFile,
  setTabExecutionStatus,
  setTabExecutionStatusPercentage,
  setConnectionSession,
  updateConnectionSession,
  updatePaneInfoAboutErrormark,
  pushMonacoValue,
  saveExecuteActiveTabParams,
  saveExecuteActiveTabInfo,
  setExamineReviewResult,
  setSdtBasicSettingData,
  setNewSdtSelectedKeysVal,
  updataPaneTabColor,
  setConnectionsAndUserSettingInfo,
  updatePaneScrollMode
} = queryTabsSlice.actions

export const executeEditorSql =
  (
    t: any,
    /** If not given, use sql in active editor */ selectedText?: string,
    /** If not given, use database info in active editor */ databaseParams?: {
    connectionId: number | string
    connectionType: DataSourceType
    databaseName: string
  }
  ): AppThunk =>
    async(dispatch, getState) => {
      const { activeTabKey, paneInfoMap, tabInfoMap } = getState().queryTabs
      const activePaneInfo = paneInfoMap[activeTabKey];
      const acticeTabInfo = tabInfoMap[activeTabKey] || {}

      if (!activePaneInfo) return
      const {
        key,
        connectionId,
        connectionType,
        databaseName,
        value,
        plSql,
        tSql,
        txMode,
      } = activePaneInfo
      const rawSql = selectedText !== undefined ? selectedText : value || ''
      const splitter = tSql
        ? new TransactSqlSplitter(rawSql)
        : new SqlSplitter(rawSql)

      const statements = plSql && !tSql ? [rawSql] : splitter.split()
      if (!connectionId || !connectionType || !statements.length) return

      const rowNum = acticeTabInfo?.resultPageSize;
      const params = {
        connectionId: connectionId,
        dataSourceType: connectionType,
        operatingObject: getOperatingObject(activePaneInfo, connectionType),
        statements,
        offset: 0,
        rowCount: tSql ? TSQL_BLOCK_SIZE : rowNum || BLOCK_SIZE,
        ...databaseParams,
        tabKey: key,
        plSql,
        databaseName,
        autoCommit: txMode === 'auto',
        actionType: 'EXECUTE',
      }
      // 执行前将执行状态置为 pending
      dispatch(setTabExecutionStatus({ key, pending: true }))
      dispatch(setTabExecutionStatusPercentage({key: key, executePercentage: 0, executeStatusMessage: `${t('sdo_executing_status')}...0%`}));
      // 执行语句
      return dispatch(queryExecuteResults(key, params)).finally(() => {
        // 执行状态置为 pending false
        dispatch(setTabExecutionStatus({ key, pending: false }))
        dispatch(setTabExecutionStatusPercentage({key: key, executePercentage: 100, executeStatusMessage: `${t('sdo_execution_completed')}`}));
      })
    }

export const explainEditorSql =
  (
    t: any,
    /** If not given, use sql in active editor */ selectedText?: string,
    /** If not given, use database info in active editor */ databaseParams?: {
    connectionId: number | string
    connectionType: DataSourceType
    databaseName: string
  },
  ): AppThunk =>
    (dispatch, getState) => {
      const { activeTabKey, paneInfoMap } = getState().queryTabs
      const activePaneInfo = paneInfoMap[activeTabKey]
      if (!activePaneInfo) return
      const {
        key,
        connectionId,
        connectionType,
        databaseName: operatingDatabase,
        schemaName,
        value,
      } = activePaneInfo
      
      const statements = selectedText !== undefined ? selectedText : value || '';
      
      if (!connectionId || !connectionType || !statements?.length) return
      const params = {
        connectionId: connectionId,
        dataSourceType: connectionType,
        databaseName: operatingDatabase,
        operatingObject: getOperatingObject({ databaseName:operatingDatabase, schemaName }, connectionType),
        statements: [statements],
        offset: 0,
        rowCount: BLOCK_SIZE,
        ...databaseParams,
        tabKey: key,
      }
      // 解释语句前 设置 pending
      dispatch(setTabExecutionStatus({ key, pending: true }))
      dispatch(setTabExecutionStatusPercentage({key: key, executePercentage: 0, executeStatusMessage: `${t('sdo_executing_status')}...0%`}));
      // 执行解释
      return dispatch(fetchExplain(params)).finally(() => {
        dispatch(setTabExecutionStatus({ key, pending: false }))
        dispatch(setTabExecutionStatusPercentage({key: key, executePercentage: 100, executeStatusMessage: `${t('sdo_execution_completed')}`}));
      })
    }

type AddPaneParams = Partial<TabInfo> &
  (Partial<MonacoEntity> & Partial<TerminalEntity>)

export const addPane =
  (params: AddPaneParams): AppThunk =>
    (dispatch, getState) => {
      const uuid = uuidv4()
      const { tabMax } = getState().login.userInfo
      const tabKeys = getState().queryTabs.tabKeyList
      if (tabKeys.length >= Number(tabMax)) {
        return message.warning(i18next.t('sdo_query_window_limit_reached'))
      }

      //在打开窗口时候获取分页条数
      if (params?.connectionId) {
        const connectionId = Number(params?.connectionId)
        if(isNaN(connectionId)){
          return message.warning(i18next.t("invalidConnectionId"))
        }
        // 右键打开连接获取相关数据（合并的一个接口）
        queryConnectionsAndUserSettings(connectionId).then(res=>{
          const { connectionExecSqlRowNum, userSettings, connectionSettings } = res ?? {}
          const { resultDisplayMode } = userSettings ?? {}

          // 结果集分页设置
          let moreActiveTabInfo: any = {
            resultPageSize: connectionExecSqlRowNum, //当前tab结果集分页条数
            isScrollLoading: resultDisplayMode === 'scroll'
          }
          if (resultDisplayMode === 'page') {
            //存储打开tab时连接设置分页条数 在页码选择器中默认展示
            moreActiveTabInfo.settingPageSize = connectionExecSqlRowNum;
          }
          //恢复之前存储连接闪回等配置信息 commit提交记录CQ-7976
          dispatch(setConnectionsAndUserSettingInfo({[connectionId]: res}))
          dispatch(addPaneWithUniqueKey({ ...params, key: uuid, moreActiveTabInfo}))
          dispatch(updateTabColor(uuid, connectionSettings))
        }).catch(err => {
          console.log('获取新建查询相关数据失败：', err);
        })
       
     }else {
       // 先同步创建基础 pane
       const basicMoreActiveTabInfo: any = {
         resultPageSize: BLOCK_SIZE, //默认分页条数
         isScrollLoading: false // 默认分页模式
       }
       dispatch(addPaneWithUniqueKey({ ...params, key: uuid, moreActiveTabInfo: basicMoreActiveTabInfo }))
       
       // 异步获取用户配置并更新
       getUserConfig().then((userConfig) => {
          // 如果配置为滚动模式，更新 pane 配置
          if (userConfig?.resultDisplayMode === 'scroll') {
            dispatch(updatePaneScrollMode({ key: uuid, isScrollLoading: true }))
          }
      }).catch(err => {
        dispatch(addPaneWithUniqueKey({ ...params, key: uuid}))
      })
    }
}

// 从上面的接口拿到connectionSettings并更新tabColor
export const updateTabColor = (key: string, data: any): AppThunk => async (dispatch) => {
  try {
    if (data?.hex) {
      dispatch(updataPaneTabColor({
        key, tabColor: {
          hex: data?.hex,
          opacity: data?.opacity
        }
      }));
    }
    else {
      dispatch(updataPaneTabColor({
        key, tabColor: undefined
      }));
    }
  } catch (error) {
    console.error("Fetch error:", error);
  }
};

// selectors
export const activePaneInfoSelector = createSelector<
  RootState,
  string,
  { [key: string]: PaneInfo },
  PaneInfo | undefined
>(
  (state) => state.queryTabs.activeTabKey,
  (state) => state.queryTabs.paneInfoMap,
  (activeTabKey, paneInfoMap) => paneInfoMap[activeTabKey],
)

/**
 * @description select active monaco paneInfo from store,
 * if current paneType is not monaco, return undefined
 */
export const activeMonacoPaneInfoSelector = createSelector<
  RootState,
  string,
  { [key: string]: TabInfo },
  { [key: string]: PaneInfo },
  PaneInfo | undefined
>(
  (state) => state.queryTabs.activeTabKey,
  (state) => state.queryTabs.tabInfoMap,
  (state) => state.queryTabs.paneInfoMap,
  (activeTabKey, tabInfoMap, paneInfoMap) => {
    const isMonaco = tabInfoMap[activeTabKey]?.paneType === 'monaco'
    const isPlSql = tabInfoMap[activeTabKey]?.paneType === 'plSql'
    const isTsql = tabInfoMap[activeTabKey]?.paneType === 'tSql'
    const isObCreateView = tabInfoMap[activeTabKey]?.paneType === 'obCreateView'
    const isCreateFnAndProcedure = tabInfoMap[activeTabKey]?.paneType === 'createFnAndProcedure'
    if (isMonaco || isPlSql || isTsql || isObCreateView || isCreateFnAndProcedure) {
      return paneInfoMap[activeTabKey]
    }
  },
)

export const queryTabsReducer = queryTabsSlice.reducer