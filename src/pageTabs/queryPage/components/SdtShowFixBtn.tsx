import React, { useCallback } from 'react'
import { useDispatch } from 'react-redux'
import { setVisibleSdt, setVisibleNodeDetail } from '../queryPageSlice'
import { Tooltip, Button } from 'antd'
import { DoubleLeftOutlined, DoubleRightOutlined } from '@ant-design/icons'
import styles from './components.module.scss'
import { useSelector } from 'src/hook/reactReduxHooks'
import { useTranslation } from 'react-i18next'

// 提取国际化字符串键名到常量中
const TOOLTIP_TITLES = {
  HIDE: "sdo_hide_sdt_tree",
  SHOW: "sdo_show_sdt_tree",
};

const SdtShowFixBtn = () => {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const { visibleSdt } = useSelector((state) => state.queryPage)

  // 提取 onClick 逻辑到外部函数，使用 useCallback 避免闭包问题和内存泄露
  const handleToggle = useCallback(() => {
    try {
      if (visibleSdt) {
        dispatch(setVisibleSdt(false));
        dispatch(setVisibleNodeDetail(false));
      } else {
        dispatch(setVisibleSdt(true));
      }
    } catch (error) {
      console.error("Dispatch error:", error);
    }
  }, [dispatch, visibleSdt]);

  return (
    <Tooltip 
      title={t(visibleSdt ? TOOLTIP_TITLES.HIDE : TOOLTIP_TITLES.SHOW)} 
      placement="left"
    >
      <Button className={styles.sdtVisibleBtn} onClick={handleToggle}>
        {
          visibleSdt 
          ? <DoubleLeftOutlined className={styles.icon}/>
          : <DoubleRightOutlined className={styles.icon}/>
        }
      </Button>
    </Tooltip>
  );
}

export default SdtShowFixBtn
