/**
 * @param prevRow 修改前的行数据
 * @param nextRow 修改后的行数据
 * @return object with altered fields
 */

import { PlainRowData } from 'src/api'
import { flattenNestedJSON } from 'src/util/flattenNestedJSON';
import type { CellKeyDownEvent } from '@ag-grid-community/core'

export const getAlteredRowData = (
  prevRow: PlainRowData,
  nextRow: PlainRowData,
) => {
  const altered: PlainRowData = {}
  for (let key of Object.keys(nextRow)) {
    if (
      [null, undefined, ''].some((v) => v === prevRow[key])&&
      [null, undefined, ''].some((v) => v === nextRow[key])
    ) {
      continue
    }
    if (nextRow[key] !== prevRow[key]) {
      altered[key] = nextRow[key]
    }
  }
  return altered
}

  //oracle自定义时候需要展示内部嵌套json 增加返回字段
export const getExpandedCurResultData = (data: any) => {
  if (!data) return [];

  const expandedData = data?.map((curItem: any) => {
    let resultData: any = {};
    let editable = true;

    for (let key in curItem) {
      //2002 为自定义字段
      if (curItem[key]?.jdbcType === 2002) {
        resultData = {
          ...resultData,
          ...(flattenNestedJSON({
            [key]: curItem[key]?.value
          }) || {})
        }
      } else if (curItem[key]?.cursorValue !== undefined) {
        // 如果包含 cursorValue，保留整个对象
        resultData = { ...resultData, [key]: curItem[key] }
      } else {
        resultData = { ...resultData, [key]: curItem[key]?.value }
      }
      if (curItem[key]?.formatValue) {
        //使用Function返回格式化数据，防止和结果集原有数据重名导致问题
        const formatValueCopy = curItem[key]?.formatValue;
        resultData[`get${key}`] = function () {
          return formatValueCopy; // 闭包只引用副本，不引用原始对象
        }
      }
      //binary字段前缀需要展示文件大小 直接拼接value 会影响真正字段值， 处理地方也比较多
      if (curItem[key]?.renderType?.includes('binary') && curItem[key]?.size) {
        resultData[`${key}CQSize`] = curItem[key]?.size
      }
      if (!curItem[key]?.editable) {
        editable = false;
      }
    }

    return {
      ...resultData,
      editable
    }
  })

  return expandedData || [];
}

//ag-grid 处理onCellDown
export const getAgGridCellDownKeys = (e: CellKeyDownEvent) => {
  const keyboardEvent = e?.event as unknown as KeyboardEvent;
  const { code, ctrlKey, altKey, shiftKey, metaKey } = keyboardEvent;
  // 组合键字符串
  let keyString = '';
  if (ctrlKey) keyString += 'ctrl+';
  if (altKey) keyString += 'alt+';
  if (shiftKey) keyString += 'shift+';
  if (metaKey) keyString += 'command+'; 

  let keyPressed = keyboardEvent?.key;
  // 判断操作系统
  const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
  if (isMac && altKey ) {
    if(code.startsWith('Key')) {
      keyPressed = code.replace('Key', '')
    }else if (code.startsWith('Digit')) {
      keyPressed = code.replace('Digit', '')
    }
  }
 
  keyString += keyPressed?.toLowerCase();
  return keyString;
}