import React from 'react';
import { GridApi } from '@ag-grid-community/core';
import { isEmpty } from 'lodash';
import { QueryResult, PlainRowData, CellRenderResultData } from 'src/api';
import { FocusedRowData } from './ResultGrid';
import { CellViewerModal } from './CellViewerModal';
import { RowViewerModal } from './RowViewerModal';
import { AddSelectedResultExportModal } from './AddSelectedResultExportModal';
import { AddResultExportModal } from './AddResultExportModal';
import { ResultAllExport } from './ResultAllExport';
import { DesensitizedFieldsModal } from './DesensitizedFieldsModal';

interface GridModalsContainerProps {
  // 模态框状态
  visibleCellViewer: boolean;
  setVisibleCellViewer: (visible: boolean) => void;
  visibleRowViewer: boolean;
  setVisibleRowViewer: (visible: boolean) => void;
  visibleCommonExport: boolean;
  setVisibleCommonExport: (visible: boolean) => void;
  visibleSelectedExport: boolean;
  setVisibleSelectedExport: (visible: boolean) => void;
  visibleExportAll: boolean;
  setVisibleExportAll: (visible: boolean) => void;
  visibleDesensitizedFields: boolean;
  setVisibleDesensitizedFields: (visible: boolean) => void;
  // 网格相关
  gridApi: GridApi | null;
  
  // 数据相关
  result: QueryResult;
  focusedRowData?: FocusedRowData;
  focusedRowIndex: number | null;
  rowViewerResultData: any[];
  curPagination: { pageNumber: number; pageSize: number; total: number | null; maxNumber: number | null };
  rowNum: number;
  filterNames: string[];
  
  // 权限相关
  permissionResult: any;
  canCopyCell: boolean;
  editable: boolean;
  connectionType?: string;
  
  // 回调函数
  updateFocusedCell: (newData: PlainRowData, oldData: PlainRowData) => void;
  downloadFocusedCell: (newData: PlainRowData, oldData: PlainRowData) => void;
  fetchFocusedCell: (newData: PlainRowData, oldData: PlainRowData) => Promise<CellRenderResultData[]>;
  nextRowIndex: () => void;
  lastRowIndex: () => void;
  handleExportAllResult: (data: any) => Promise<any>;
  applyDataExportPermission: (exportType?: string) => Promise<any>;
  setRowViewerResultData: (data: any[]) => void;
  
  // 其他配置
  type?: 'explain' | 'execute';
  isExplain: boolean;
  doubleCheckType: any;
}

/**
 * 网格模态框容器组件
 * 统一管理所有模态框的渲染逻辑
 */
export const GridModalsContainer: React.FC<GridModalsContainerProps> = (props) => {
  const {
    // 模态框状态
    visibleCellViewer,
    setVisibleCellViewer,
    visibleRowViewer,
    setVisibleRowViewer,
    visibleCommonExport,
    setVisibleCommonExport,
    visibleSelectedExport,
    setVisibleSelectedExport,
    visibleExportAll,
    setVisibleExportAll,
    visibleDesensitizedFields,
    setVisibleDesensitizedFields,
    // 其他 props
    gridApi,
    result,
    focusedRowData,
    focusedRowIndex,
    rowViewerResultData,
    curPagination,
    rowNum,
    filterNames,
    permissionResult,
    canCopyCell,
    editable,
    connectionType,
    updateFocusedCell,
    downloadFocusedCell,
    fetchFocusedCell,
    nextRowIndex,
    lastRowIndex,
    handleExportAllResult,
    applyDataExportPermission,
    setRowViewerResultData,
    type,
    isExplain,
    doubleCheckType,
  } = props;

  const { detailedResultData } = result || {};

  return (
    <>
      {/* 单元格查看器模态框 */}
      <CellViewerModal
        gridApi={gridApi}
        rowIndex={focusedRowIndex}
        rowData={focusedRowData}
        initRowData={isEmpty(focusedRowData?._initData) ? focusedRowData : focusedRowData?._initData}
        resultData={detailedResultData}
        editable={editable && connectionType !== 'DB2'}
        allowResultCopy={permissionResult?.resultCellCopy}
        visible={visibleCellViewer}
        allowBinaryCellDownload={permissionResult?.resultSetBinaryFileDownload}
        setVisible={setVisibleCellViewer}
        updateFocusedCell={updateFocusedCell}
        downloadFocusedCell={downloadFocusedCell}
        fetchFocusedCell={fetchFocusedCell}
        type={type!}
        isExplain={isExplain}
      />

      {/* 行查看器模态框 */}
      {visibleRowViewer && (() => {
        // 计算绝对索引：第二页开始每次减一修正偏移
        const pageNumber = curPagination?.pageNumber || 1;
        const pageSize = curPagination?.pageSize || rowNum;
        const offset = pageNumber > 1 ? pageNumber - 1 : 0;
        const absoluteRowIndex = (pageNumber - 1) * pageSize + (focusedRowIndex || 0) + offset;
        const currentRowData = rowViewerResultData[absoluteRowIndex] || {};
        const nextAbsoluteIndex = absoluteRowIndex + 1;
        const isLastRow = nextAbsoluteIndex >= rowViewerResultData.length;

        return (
          <RowViewerModal
            gridApi={gridApi}
            rowData={currentRowData}
            resultData={detailedResultData}
            editable={editable && connectionType !== 'DB2'}
            visible={visibleRowViewer}
            setVisible={setVisibleRowViewer}
            updateFocusedCell={updateFocusedCell}
            downloadFocusedCell={downloadFocusedCell}
            fetchFocusedCell={fetchFocusedCell}
            rowIndex={focusedRowIndex}
            nextRowIndex={nextRowIndex}
            lastRowIndex={lastRowIndex}
            isLastRowIndex={isLastRow}
            setRowViewerResultData={() => {
              setRowViewerResultData([]);
            }}
            resultAllowCopy={canCopyCell}
          />
        );
      })()}

      {/* 选中结果导出模态框 */}
      <AddSelectedResultExportModal
        result={result}
        visible={visibleSelectedExport}
        setVisible={setVisibleSelectedExport}
        gridApi={gridApi}
        permissionResult={permissionResult}
      />

      {/* 结果导出模态框 */}
      <AddResultExportModal
        result={result}
        visible={visibleCommonExport}
        setVisible={setVisibleCommonExport}
        applyDataExportPermission={applyDataExportPermission}
        permissionResult={permissionResult}
      />

      {/* 全部结果导出模态框 */}
      <ResultAllExport
        result={result}
        visible={visibleExportAll}
        setVisible={setVisibleExportAll}
        hanldeExportAll={handleExportAllResult}
        applyDataExportPermission={applyDataExportPermission}
        permissionResult={permissionResult}
      />

      {/* 脱敏字段模态框 */}
      <DesensitizedFieldsModal
        result={result}
        visible={visibleDesensitizedFields}
        setVisible={setVisibleDesensitizedFields}
        doubleCheckType={doubleCheckType}
        filterNames={filterNames}
      />
    </>
  );
};