import { useState } from 'react';

export interface GridModalsState {
  visibleCellViewer: boolean;
  visibleRowViewer: boolean;
  visibleCommonExport: boolean;
  visibleSelectedExport: boolean;
  visibleExportAll: boolean;
  visibleDesensitizedFields: boolean;
}

export interface GridModalsActions {
  setVisibleCellViewer: (visible: boolean) => void;
  setVisibleRowViewer: (visible: boolean) => void;
  setVisibleCommonExport: (visible: boolean) => void;
  setVisibleSelectedExport: (visible: boolean) => void;
  setVisibleExportAll: (visible: boolean) => void;
  setVisibleDesensitizedFields: (visible: boolean) => void;
}

export interface UseGridModalsReturn extends GridModalsState, GridModalsActions {}

/**
 * 管理网格组件中所有模态框的可见性状态的自定义 Hook
 * 
 * @returns 包含所有模态框状态和控制函数的对象
 */
export const useGridModals = (): UseGridModalsReturn => {
  const [visibleCellViewer, setVisibleCellViewer] = useState(false);
  const [visibleRowViewer, setVisibleRowViewer] = useState(false);
  const [visibleCommonExport, setVisibleCommonExport] = useState(false);
  const [visibleSelectedExport, setVisibleSelectedExport] = useState(false);
  const [visibleExportAll, setVisibleExportAll] = useState(false);
  const [visibleDesensitizedFields, setVisibleDesensitizedFields] = useState(false);

  return {
    // 状态
    visibleCellViewer,
    visibleRowViewer,
    visibleCommonExport,
    visibleSelectedExport,
    visibleExportAll,
    visibleDesensitizedFields,
    
    // 控制函数
    setVisibleCellViewer,
    setVisibleRowViewer,
    setVisibleCommonExport,
    setVisibleSelectedExport,
    setVisibleExportAll,
    setVisibleDesensitizedFields,
  };
};