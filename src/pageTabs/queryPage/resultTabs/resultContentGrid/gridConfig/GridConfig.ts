import { AgGridReactProps } from '@ag-grid-community/react'
import { ColDef } from '@ag-grid-community/core'
import { InfiniteRowModelModule } from '@ag-grid-community/infinite-row-model'
import locale from './locale'
import locale_en from './locale_en'
import { BLOCK_SIZE } from 'src/constants'
import {
  BooleanEditor,
  SelectEditor,
  SelectInputEditor,
  SimpleMarkRenderer,
  SimpleTextRenderer,
  TextEditor,
  ColumnSelectEditor,
  ColumnMultiSelectEditor,
  ColumnSpecialMultiSelector,
  AntdSelectEditor,
  NumericEditor,
  RowIndexRenderer,
  SimpleTextRendererWithContextMenu,
  RowIndexRendererWithContextMenu,
  RowIndexFromPropsRendererWithContextMenu
} from 'src/components'

export const GridConfigBase = (sys_locales: string): AgGridReactProps => {
  return (
    {
      localeText: sys_locales === 'zh'? locale : locale_en,
      rowHeight: 28,
      headerHeight: 32,
      rowSelection: 'multiple',
      tooltipShowDelay: 500,
      enableCellTextSelection: true,
      ensureDomOrder: true,
      frameworkComponents: {
        // renderer
        booleanRenderer: SimpleMarkRenderer,
        simpleTextRenderer: SimpleTextRenderer,
        SimpleTextRendererWithContextMenu,
        RowIndexRenderer,
        RowIndexRendererWithContextMenu,
        RowIndexFromPropsRendererWithContextMenu,
        // editor
        textEditor: TextEditor,
        selectEditor: SelectEditor,
        selectInputEditor: SelectInputEditor,
        booleanEditor: BooleanEditor,
        columnSelectEditor: ColumnSelectEditor,
        columnMultiSelectEditor: ColumnMultiSelectEditor,
        columnSpecialMultiSelector: ColumnSpecialMultiSelector,
        antdSelectEditor:AntdSelectEditor,
        numericEditor:NumericEditor
      },
      /**
       * If true, then dots in field names (e.g. address.firstline) are not treated as deep references.
       * Allows you to use dots in your field name if you prefer.
       */
      suppressFieldDotNotation: true,
      animateRows: false,
      // 自定义选择配置 - 禁用默认选择行为
      suppressRowClickSelection: true,  // 禁用默认行选择
      enableRangeSelection: false,      // 禁用AG Grid内置区域选择
      // suppressClickEdit: true,
    }
  )
}

export const defaultColDef: ColDef = {
  editable: true,
  resizable: true,
  suppressKeyboardEvent: (params) => {
    const { editing, event } = params
    // 非编辑状态下禁用退格键
    return !editing && event.code === 'Backspace'
  },
  tooltipValueGetter: ({ value }) => value,
  cellClass: ({ colDef }) => {
    if (!colDef?.headerComponentParams){
      return 'aggrid-cell'
    }
    if (colDef?.headerComponentParams.isDesens) {
      return 'aggrid-cell aggrid-cell-isDesens'
    }
    return 'aggrid-cell'
  },
}

export const infiniteModeOptions = {
  modules: [InfiniteRowModelModule],
  editType: 'fullRow',
  rowModelType: 'infinite',
  infiniteInitialRowCount: 1,
  // cacheBlockSize: 100,
  // maxBlocksInCache: 5, 最大缓存数量 设置会回滚触发请求导致执行行数空白CQ-1297
  blockLoadDebounceMillis: 150,
  // rowBuffer:50,
  // cacheOverflowSize: 0,
  // maxBlocksInCache: 0,
  // suppressAnimationFrame: true,  // 禁用动画帧
  // debounceVerticalScrollbar: true,  
}

export { BLOCK_SIZE }
