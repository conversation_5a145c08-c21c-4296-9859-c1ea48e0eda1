import React from 'react';
import { Menu, Dropdown } from 'antd';
import type { ICellRendererParams } from '@ag-grid-community/core';

export interface MenuItem {
  key: string;
  label: React.ReactNode;
  disabled?: boolean;
  handler?: (params: ICellRendererParams) => void;
  children?: MenuItem[];
  type?: 'divider';
}

interface GlobalContextMenuProps {
  visible: boolean;
  position: { x: number; y: number };
  menuItems: MenuItem[];
  contextData: ICellRendererParams | null;
  onClose: () => void;
  onItemClick: (handler: (params: ICellRendererParams) => void, key: React.Key) => void;
}

export const GlobalContextMenu: React.FC<GlobalContextMenuProps> = ({
  visible,
  position,
  menuItems,
  onClose,
  onItemClick,
}) => {

  const handleMenuClick = (info: { key: React.Key }) => {
    const findAndExecute = (items: MenuItem[]) => {
      for (const item of items) {
        if (item.key === info.key && item.handler) {
          onItemClick(item.handler, info.key);
          return true;
        }
        if (item.children) {
          if (findAndExecute(item.children)) return true;
        }
      }
      return false;
    }
    findAndExecute(menuItems);
    onClose();
  };

  const renderMenuItems = (items: MenuItem[]): React.ReactElement[] => {
    return items.map(item => {
      if (item.type === 'divider') {
        return <Menu.Divider key={item.key} />;
      }
      if (item.children && item.children.length > 0) {
        return (
          <Menu.SubMenu key={item.key} title={item.label} disabled={item.disabled}>
            {renderMenuItems(item.children)}
          </Menu.SubMenu>
        );
      }
      return (
        <Menu.Item key={item.key} disabled={item.disabled}>
          {item.label}
        </Menu.Item>
      );
    });
  };

  const menu = (
    <Menu onClick={handleMenuClick}>
      {renderMenuItems(menuItems)}
    </Menu>
  );

  return (
    <Dropdown
      overlay={menu}
      visible={visible}
      onVisibleChange={(flag) => {
        if (!flag) {
          onClose();
        }
      }}
      trigger={['contextMenu']}
      // We manually control the position via a fixed div, so this is not strictly needed
      // but it's good practice to have it.
      placement="bottomLeft" 
    >
      {/* This div is the invisible trigger for the Dropdown. 
          It's positioned absolutely at the cursor's location. */}
      <div
        style={{
          position: 'fixed',
          left: position.x,
          top: position.y,
          width: 0,
          height: 0,
        }}
      />
    </Dropdown>
  );
};