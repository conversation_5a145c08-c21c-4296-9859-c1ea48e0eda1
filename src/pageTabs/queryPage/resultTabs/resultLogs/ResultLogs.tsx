import React, { useRef, useEffect, memo, useState, useMemo } from 'react'
import { VariableSizeList } from 'react-window'
import { useSelector } from 'src/hook'
import { LogMessage } from './LogMessage'
import styles from './index.module.scss'
import { calculateRowHeight } from 'src/util'
import InfiniteLoader from 'react-window-infinite-loader'
import AutoSizer from 'react-virtualized-auto-sizer'
import { isEqual } from 'lodash'

interface ResultLogProps {
  queryKey: string
  activeResultTabKey: string
}
export const ResultLogs: React.FC<ResultLogProps> = memo(({ queryKey, activeResultTabKey }) => {
  const tabLog = useSelector((state) => state.logs[queryKey])
  const logs = useMemo(() => tabLog?.logs || [], [tabLog])
  const [loadedKeys, setLoadedKeys] = useState<number[]>([])
  const [lastMessageId, setLastMessageId] = useState<string | undefined>('');
  const listRef = useRef<VariableSizeList>(null)

  useEffect(() => {
    if (logs.length > 0) {
      const keys = logs?.map((_: any, index: number) => index)
      if (!isEqual(loadedKeys, keys)) {
        setLoadedKeys(keys)
      }
      const messageId = logs[logs.length - 1]?.messageId
      if (messageId) {
        setLastMessageId(messageId)
      }
      // 滚动到最后一行
      if (listRef.current) {
        listRef.current.scrollToItem(logs.length - 1, 'end')
      }
    }

  }, [logs, activeResultTabKey, loadedKeys])

  // 添加清理 effect，在组件卸载时执行
  useEffect(() => {
    return () => {
      // 组件卸载时清理状态，避免内存泄漏
      setLoadedKeys([])
      setLastMessageId(undefined)
    }
  }, [])

  // 重新渲染时，使得VariableSizeList重新计算itemSize
  useEffect(()=>{
    setLoadedKeys([])
  },[logs,activeResultTabKey])
  
  // 监听页面大小变化
  window.onresize = () => {
    !!loadedKeys?.length && setLoadedKeys([])
  }

  const handleLoadMore = (_startIndex: number, _stopIndex: number) => {
    // 该方法不会每次都执行,当日志删除后重新加载内容会出错,所以不要依赖该方法来更新itemData
    return Promise.resolve()
  }

  const getItemSize = (index: number, width: string): number => {
    if (!logs?.[index]) {
      return 0
    }
    const { error, executionWarning } = logs?.[index]
    let text = ""
    let height = 0
    if (!executionWarning && !error) {
      height = 50
    } else if (error && executionWarning) {
      text = error + executionWarning
      height = calculateRowHeight(text, width, 100)
    } else {
      text = error ? error : executionWarning
      height = calculateRowHeight(text, width, 72)
    }
    return height
  }

  return (
    <div className={styles.logWrapper}>
      <AutoSizer>
        {({ height, width }) => (
          <div
            style={{ height, width }}
          >
            <InfiniteLoader
              isItemLoaded={(index) => loadedKeys.includes(index)}
              itemCount={logs?.length ?? 0}   // 列表中的行数；如果实际行数未知，可以任意取大Infinity
              loadMoreItems={handleLoadMore}    
            >
              {({ onItemsRendered, ref }) => (
                <VariableSizeList
                  ref={(list) => {
                    if (typeof ref === 'function') {
                      ref(list)
                    }
                    (listRef as React.MutableRefObject<VariableSizeList | null>).current = list
                  }}
                  height={height}
                  width={width}
                  onItemsRendered={onItemsRendered}
                  itemCount={logs?.length ?? 0}
                  itemData={logs}
                  itemSize={(index: number) => {
                    // width去掉padding
                    const realWidth = width ? (width - 48 - 8) + 'px' : '800px'
                    return getItemSize(index, realWidth)
                  }}
                >
                  {({ data, index, style }) => {
                    if (!data[index]) {
                      return null
                    }
                    let customStyle: any = { padding: '16px 24px 0' }
                    return (
                      <div style={{ ...style, ...customStyle }}>
                        <LogMessage
                          key={index}
                          log={data[index]}
                          lastMessageId={lastMessageId}
                          index={index}
                          queryKey={queryKey}
                        />
                      </div>
                    )
                  }}
                </VariableSizeList>
              )}
            </InfiniteLoader>
          </div>
        )}
      </AutoSizer>
    </div>
  )
}, (prevProps, nextProps) => {
  // 自定义比较函数，确保在 queryKey 或 activeResultTabKey 变化时重新渲染
  return prevProps.queryKey === nextProps.queryKey &&
         prevProps.activeResultTabKey === nextProps.activeResultTabKey
})
