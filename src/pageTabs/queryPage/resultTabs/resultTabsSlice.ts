import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit'
import { chunk } from 'lodash'
import {
  executeSqlStatement,
  explainSqlStatement,
  QueryResult,
  StatementExecuteParams,
} from 'src/api'
import { AppThunk, RootState } from 'src/store'
import { addLog } from 'src/store/extraSlice/logsSlice'
import type { QueryBase, QueryLogItem } from 'src/types'
import { sleep } from 'src/util'
import { formatExecuteResult } from 'src/util/grid'
import { ExecuteActiveTabInfo } from 'src/pageTabs/queryPage/queryTabs/queryTabsSlice'

interface ResultTabsState {
  /** queryTabKey => resultKey[] */
  executeKeyListMap: Record<string, string[]>
  explainKeyListMap: Record<string, string[]>
  /** resultTabKey => ResultTab */
  resultTabMap: Record<string, ResultTab>
  /** queryTabKey => activeResultTabKey */
  activeResultKeyMap: Record<string, string>
  /** queryTabKey => 查询池当前 segment 执行状态信息 */
  segmentMsg: Record<string, SegmentMsg[] | undefined>
  /** 结果面板的高度 */
  height: number

  segmentPositionMsg: SegmentPositionMsg[]
  resultCount: number
  errorJump: boolean
  // errorPosition: any  // 接口获取的错误信息
  lastErrorPosition: { [key: string]: any }
  isRun: boolean // 是否执行
  doubleClickCell?: any // 双击单元格信息
  needRefreshResultData?: boolean // 是否重新获取结果集数据
  deferExecuteResultMap:  {[key: string]: ExecuteActiveTabInfo}//monaco执行操作结果
}

const initialState: ResultTabsState = {
  executeKeyListMap: {},
  explainKeyListMap: {},
  resultTabMap: {},
  activeResultKeyMap: {},
  segmentMsg: {},
  segmentPositionMsg: [],
  resultCount: 0,
  height: 0,
  errorJump: false,
  lastErrorPosition: {},
  isRun: false,
  doubleClickCell: {},
  needRefreshResultData: false,
  deferExecuteResultMap: {}
}

export const fetchExplain = createAsyncThunk<
  { queryKey: string; explains: QueryResult[] },
  StatementExecuteParams,
  { state: RootState }
>('resultTabs/fetchExplain', async (params, { dispatch, getState }) => {
  const queryKey = getState().queryTabs.activeTabKey
  const keyList = getState().resultTabs.explainKeyListMap[queryKey] || []
  dispatch(
    setActiveResultTabKey({
      queryTabKey: queryKey,
      resultTabKey: `log/${queryKey}`,
    }),
  )
  dispatch(resetExplainKeyList(queryKey))
  dispatch(deleteResultTabsByKeys(keyList))

  const explains = await explainSqlStatement(params)
  return {
    queryKey,
    explains,
  }
})

export const resultTabsSlice = createSlice({
  name: 'resultTabs',
  initialState,
  reducers: {
    setExecuteKeyList(
      state,
      action: PayloadAction<{ key: string; list: string[] }>,
    ) {
      const { executeKeyListMap } = state
      const { key, list } = action.payload
      executeKeyListMap[key] = list
    },
    resetExplainKeyList(state, action: PayloadAction<string>) {
      const queryKey = action.payload
      state.explainKeyListMap[queryKey] = []
    },
    updateResultTabs(state, action: PayloadAction<ResultTab[]>) {
      const { resultTabMap } = state
      action.payload.forEach(({ key, info }) => {
        resultTabMap[key] = { key, info }
      })
    },
    setPageTotalByQueryKey(state, action: PayloadAction<{ queryKey: string, pageTotal: number }>) {
      const { queryKey,  pageTotal } = action.payload;
      const resultKey = state.activeResultKeyMap[queryKey]
      if (state.resultTabMap[resultKey]) {
        const { key, info } = state.resultTabMap[resultKey]
        state.resultTabMap[resultKey] = {
          key,
          info: {
            ...info,
            pageTotal,
          }
        }
      }
    },
    saveResultCount(state, action: PayloadAction<number>) {
      state.resultCount = action.payload
    },

    // 设置双击单元格信息
    setDoubleClickCell(state, action: PayloadAction<any>) {
      state.doubleClickCell = action.payload
    },

    // 删除指定 keyList 里所有 key 对应的 resultTabMap value
    deleteResultTabsByKeys(state, action: PayloadAction<string[]>) {
      const { resultTabMap } = state
      action.payload.forEach((key) => {
        delete resultTabMap[key]
      })
    },

    // 删除指定 queryTabKey 对应的 executeKeyList
    deleteExecuteKeyList(state, action: PayloadAction<string>) {
      const { executeKeyListMap } = state
      delete executeKeyListMap[action.payload]
    },

    setActiveResultTabKey(
      state,
      action: PayloadAction<{ queryTabKey: string; resultTabKey: string }>,
    ) {
      const { queryTabKey, resultTabKey } = action.payload
      state.activeResultKeyMap[queryTabKey] = resultTabKey
    },

    // 设置指定 queryTab 下的分块执行状态信息
    addSegmentMsg(state, action: PayloadAction<SegmentMsg>) {
      const segmentMsg = action.payload
      const { tabKey } = segmentMsg
      const  msgList = state.segmentMsg[tabKey]
      if (!msgList) {
        state.segmentMsg[tabKey] = [segmentMsg]
        return
      }
      if (
        !msgList.find(
          ({ segmentIndex }) => segmentIndex === segmentMsg.segmentIndex,
        )
      ) {
        msgList.push(segmentMsg)
      }
    },

    addSegmentPositionMsg(state, action: PayloadAction<SegmentPositionMsg[]>) {
      state.segmentPositionMsg = action.payload;
    },
    setErrorJump(state, action: PayloadAction<boolean>) {
      state.errorJump = action.payload;
    },
    setLastErrorPosition(state, action: PayloadAction<any>) {
      state.lastErrorPosition = action.payload;
    },
    setIsRun(state, action: PayloadAction<any>) {
      state.isRun = action.payload;
    },
    resetSegmentMsg(state, action: PayloadAction<string | undefined>) {
      const tabKey = action.payload
      if (!tabKey) {
        state.segmentMsg = {}
        return
      }
      state.segmentMsg[tabKey] = []
    },
    //删除结果集 | 执行计划解释
    removeResultTabPane(state, action: PayloadAction<{targetResultKey: string; queryTabKey: string}>) {
      const {resultTabMap, executeKeyListMap, explainKeyListMap} = state;

      const {targetResultKey, queryTabKey } = action.payload;

      let targetQueryResultKeys = executeKeyListMap[queryTabKey] || [];
      let targetExplainKeys = explainKeyListMap[queryTabKey] || [];
      //result key && explain key不存在
      if (!targetQueryResultKeys.includes(targetResultKey) && !targetExplainKeys.includes(targetResultKey)) return

      // 删除tab
      delete resultTabMap[targetResultKey];

      // 获取删除前的所有 tab 列表，用于确定前一个 tab
      const allTabs = [`log/${queryTabKey}`, ...targetQueryResultKeys, ...targetExplainKeys];

      //结果集
      if (targetQueryResultKeys.includes(targetResultKey)) {
        targetQueryResultKeys = targetQueryResultKeys.filter(key => key !== targetResultKey)
        state.executeKeyListMap = {...executeKeyListMap, [queryTabKey]: targetQueryResultKeys}

      }else {
        //执行计划
        targetExplainKeys = targetExplainKeys.filter(key => key !== targetResultKey)
        state.explainKeyListMap = {...explainKeyListMap, [queryTabKey]: targetExplainKeys}
      }

      // 计算要激活的 tab：优先选择前一个，如果没有前一个则选择下一个，最后才选择执行日志
      const updatedAllTabs = [`log/${queryTabKey}`, ...targetQueryResultKeys, ...targetExplainKeys];
      const currentActiveTab = state.activeResultKeyMap[queryTabKey];

      let newActiveTab: string;
      if (currentActiveTab === targetResultKey) {
        // 如果删除的是当前活跃的 tab
        const currentIndex = allTabs.indexOf(targetResultKey);
        if (currentIndex > 0) {
          // 有前一个 tab，选择前一个
          const previousTab = allTabs[currentIndex - 1];
          newActiveTab = updatedAllTabs.includes(previousTab) ? previousTab : updatedAllTabs[0];
        } else if (updatedAllTabs.length > 1) {
          // 没有前一个但有其他 tab，选择第一个非日志 tab 或日志 tab
          newActiveTab = updatedAllTabs[1] || updatedAllTabs[0];
        } else {
          // 只剩下执行日志
          newActiveTab = `log/${queryTabKey}`;
        }
      } else {
        // 删除的不是当前活跃的 tab，保持当前活跃 tab 不变
        newActiveTab = currentActiveTab;
      }

      state.activeResultKeyMap[queryTabKey] = newActiveTab;
    },
    // 设置结果面板高度
    setResultPanelHeight(state, action: PayloadAction<number>) {
      state.height = action.payload
    },
    // 设置需要重新获取结果集数据
    setNeedRefreshResultData(state, action: PayloadAction<boolean>) {
      state.needRefreshResultData = action.payload
    },
    //derferExecute 执行接口存储结果，querytabs存储 刷新后结果会依旧存在
    setDeferExecuteResultMapping(state,action: PayloadAction<{ key: string; data: any }>) {
      const { deferExecuteResultMap } = state
      const { key, data } = action.payload
      deferExecuteResultMap[key] = data
    },
    //清空当前 、清空所有、清空当前标签外的其他
    removeAllOrCloseOtherResultTab(state, action: PayloadAction<{closedTabType: 'all' | 'notCurrentSelected' | 'current', tabKey?: string}>) {
      const { closedTabType = 'current', tabKey } = action.payload;

      //关闭所有窗口操作
      if (closedTabType === 'all') {
        // 完全重置所有状态
        state.executeKeyListMap = {};
        state.explainKeyListMap = {};
        state.resultTabMap = {};
        state.activeResultKeyMap = {};
        state.deferExecuteResultMap = {};
        state.segmentMsg = {};

        //关闭当前窗口操作
      }else if (closedTabType === 'current' && tabKey) {
        console.log('关闭当前窗口操作')
        // 获取需要清理的结果键列表
        const executeKeyList = state.executeKeyListMap[tabKey] || [];
        const explainKeyList = state.explainKeyListMap[tabKey] || [];
        const allResultKeys = [...executeKeyList, ...explainKeyList];

        // 清理 resultTabMap 中的相关数据
        allResultKeys.forEach(key => {
          delete state.resultTabMap[key];
        });

        // 清理各种映射关系
        delete state.executeKeyListMap[tabKey];
        delete state.explainKeyListMap[tabKey];
        delete state.activeResultKeyMap[tabKey];
        delete state.deferExecuteResultMap[tabKey];
        delete state.segmentMsg[tabKey];

        //获取其他标签操作
      }else if (closedTabType === 'notCurrentSelected' && tabKey) {

        // 保留当前标签页的数据，清理其他所有数据
        const currentExecuteKeyList = state.executeKeyListMap[tabKey] || [];
        const currentExplainKeyList = state.explainKeyListMap[tabKey] || [];
        const currentActiveResultKey = state.activeResultKeyMap[tabKey];
        const currentDeferExecuteResult = state.deferExecuteResultMap[tabKey];
        const currentSegmentMsg = state.segmentMsg[tabKey];

        // 保留当前标签页的 resultTabMap 数据
        const currentResultTabMap: Record<string, ResultTab> = {};
        [...currentExecuteKeyList, ...currentExplainKeyList].forEach(key => {
          if (state.resultTabMap[key]) {
            currentResultTabMap[key] = state.resultTabMap[key];
          }
        });

        // 重置为只包含当前标签页的数据
        state.executeKeyListMap = { [tabKey]: currentExecuteKeyList };
        state.explainKeyListMap = { [tabKey]: currentExplainKeyList };
        state.resultTabMap = currentResultTabMap;
        state.activeResultKeyMap = { [tabKey]: currentActiveResultKey };
        state.deferExecuteResultMap = currentDeferExecuteResult ? { [tabKey]: currentDeferExecuteResult } : {};
        state.segmentMsg = currentSegmentMsg ? { [tabKey]: currentSegmentMsg } : {};
      }
    },
    // 创建 cursor 跳转的 Tab
    createCursorResultTab(
      state,
      action: PayloadAction<{
        queryKey: string
        cursorData: QueryResult
        fieldName: string
        rowIndex: number
      }>
    ) {
      const { queryKey, cursorData, fieldName, rowIndex } = action.payload
      const tabKey = `cursor/${queryKey}-${fieldName}-${rowIndex}-${Date.now()}`

      // 格式化 cursor 数据，确保有正确的 detailedResultData
      let formattedCursorData = cursorData
      if (cursorData.resultData && !cursorData.detailedResultData) {
        // 使用 formatExecuteResult 来处理数据
        const [formatted] = formatExecuteResult([cursorData])
        formattedCursorData = formatted
      }

      // 添加到 resultTabMap
      state.resultTabMap[tabKey] = {
        key: tabKey,
        info: formattedCursorData
      }

      // 添加到对应查询的执行结果列表
      const executeKeyList = state.executeKeyListMap[queryKey] || []
      state.executeKeyListMap[queryKey] = [...executeKeyList, tabKey]

      // 设置为活跃 tab
      state.activeResultKeyMap[queryKey] = tabKey
    },

  },
  extraReducers: (builder) => {
    builder.addCase(fetchExplain.fulfilled, (state, action) => {
      const { queryKey, explains } = action.payload
      const resultTabs = explains.map((info, index) => ({
        key: `explain/${queryKey}-${index}`,
        info,
      }))
      const list = resultTabs.map(({ key }) => key)
      // 更新 resultTabMap
      resultTabs.forEach(({ key, info }) => {
        state.resultTabMap[key] = { key, info }
      })
      // 设置 explain keyList
      state.explainKeyListMap[queryKey] = list
      // 设置 activeResultTab 为第一个解释集
      state.activeResultKeyMap[queryKey] = list[0] || `log/${queryKey}`
    })
  },
})

export const {
  setExecuteKeyList,
  resetExplainKeyList,
  updateResultTabs,
  setPageTotalByQueryKey,
  saveResultCount,
  deleteResultTabsByKeys,
  deleteExecuteKeyList,
  setActiveResultTabKey,
  addSegmentMsg,
  addSegmentPositionMsg,
  resetSegmentMsg,
  removeResultTabPane,
  setResultPanelHeight,
  setErrorJump,
  setLastErrorPosition,
  setIsRun,
  setDoubleClickCell,
  setNeedRefreshResultData,
  setDeferExecuteResultMapping,
  removeAllOrCloseOtherResultTab,
  createCursorResultTab
} = resultTabsSlice.actions


/**
 * @description 执行语句, 获取该查询面板下的结果集列表
 * @param key 该查询面板的唯一标识 queryTabKey
 * @param params 执行语句接口 PUT 请求的 body 参数
 */
export const queryExecuteResults =
  (key: string, params: StatementExecuteParams): AppThunk<Promise<void>> =>
    (dispatch, getState) => {
      // 分批次渲染logs
      const queueLogs = async (logs: QueryLogItem[][]) => {
      for (let i =0; i< logs.length; i++) {
          dispatch(addLog(logs[i]))
          await sleep(50)
        }
      }
      dispatch(resetResultList(key))
      return executeSqlStatement(params)
        .then((data) => {
          const { executionInfos, segmentExecutionLog } = data
          const executeSuccess = segmentExecutionLog?.success
          const resultCount = executionInfos[0]?.response?.resultData?.length;
          dispatch(saveResultCount(resultCount))
          let logs: QueryLogItem[] = []
          const resultTabs = executionInfos.map((info, index) => {
            logs.push(info.executeLogInfo.message)
            return ({
              key: `execute/${key}-${index}`,
              info: info.response,
            })
          })
          const logsChunks = chunk(logs, 20)
          queueLogs(logsChunks)
          const list = resultTabs.map(({ key }) => key)
          // 先更新 resultTabs, 再设置 keyList
          dispatch(updateResultTabs(resultTabs))
          dispatch(setExecuteKeyList({ key, list }))
          dispatch(
            setActiveResultTabKey({
              queryTabKey: key,
            resultTabKey: (executeSuccess && list[0]) ? list[0] : `log/${key}` ,
            }),
          )
        })
      .catch(() => {})
    }

export const resetResultList =
  (key: string): AppThunk =>
    (dispatch, getState) => {
      const keyList = getState().resultTabs.executeKeyListMap[key] || []
      // 先清空 keyList, 再删除对应的 resultTabs
      dispatch(
        setActiveResultTabKey({
          queryTabKey: key,
          resultTabKey: `execute/${key}`,
        }),
      )
      dispatch(setExecuteKeyList({ key, list: [] }));
      dispatch(setDeferExecuteResultMapping({key, data: null}));
      dispatch(deleteResultTabsByKeys(keyList))
    }

export const resultTabsReducer = resultTabsSlice.reducer

export const activeResultSelector = (state: RootState) => {
  const queryKey = state.queryTabs.activeTabKey
  const resultKey = state.resultTabs.activeResultKeyMap[queryKey]
  const result = state.resultTabs.resultTabMap[resultKey]
  return result
}

export const paneInfoMapSelector = (state: RootState) => {
  return state.queryTabs?.paneInfoMap?.[state.queryTabs?.activeTabKey]
}

//types
export interface ResultTab {
  /** resultTabKey */
  key: string
  info: QueryResult
}

export interface SegmentMsg extends QueryBase {
  tabKey: string
  success: boolean
  /** 分块起始 index */
  segmentIndex: number
  /** 终止语句在分块中的 index */
  statementIndex: number
}

export interface SegmentPositionMsg{
  tabKey: string
  success: boolean
  messageId: string
  /** 错误语句位置 {} */
  position: {
    startLine: number, startCol: number, stopLine: number, stopCol: number
  }
}
