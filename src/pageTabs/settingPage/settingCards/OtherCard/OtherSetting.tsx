import React, { useContext, useEffect, useState, memo } from 'react'
import {
  FormOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons'
import {
  message,
  Form,
  Tooltip,
  Switch,
  Select,
  Spin,
  Input,
} from 'antd'
import { useTranslation } from 'react-i18next'
import { FormInstance } from 'antd/lib/form'
import {
  getVersionIsCommunity,
  getAllDatasource,
  getFilterResource,
  postFilterResource,
  postForceLogin,
  getCascadeDefault,
  getForceLogin
} from 'src/api'
import { useRequest, useSelector } from 'src/hook'
import { SelectWithAdd } from 'src/components/SelectWithAdd'
import { SettingFormContext } from '../../SettingFormContext'
import styles from '../index.module.scss'

export const OtherSetting = memo(() => {

  const { t } = useTranslation()
  const isLangEn = useSelector(state => state.login.locales) === 'en';
  const [editing, setEditing] = useState('')
  const form = useContext(SettingFormContext) as FormInstance
  const editOtp = !(form.getFieldValue("loginSetting") === "FORCE_OTP_LOGIN")
  const editPhone = !(form.getFieldValue("loginSetting") === "FORCE_SMS_LOGIN")

  const bindTitle = {
    forceOtpBing: t('systemManagement.system.other.forceOtpBing'),
    forcePhoneBing: t('systemManagement.system.other.forcePhoneBing')
  }

  // 获取数据源 options
  const { data: datasourceOptions } = useRequest(getAllDatasource, {
    onSuccess(res) {
      const val = res?.[0]?.value;
      if (val) {
        getFilterResourceRun(val).then((res) => {
          form.setFieldsValue({
            sysFilterLib: {
              dataSource: val,
              filterResources: res,
            }
          });
        })
      }
    },
    formatResult(res = []) {
      return res.map((item: any) => ({ label: item, value: item }))
    },
  })

  // 获取数据源的过滤库
  const { data: filterResourceOptions, run: getFilterResourceRun } = useRequest(getFilterResource, {
    manual: true,
    onSuccess(res) {
      form.setFieldsValue({
        sysFilterLib: {
          filterResources: res
        }
      });
    }
  })

  //级联
  const { data: getForceLoginData, loading: forceLoaginLoading, run: runGetForceLogin } = useRequest(getForceLogin, {
    onSuccess: (res) => {
      form.setFieldsValue({ forceOtpBing: res?.forceOtpBing, forcePhoneBing: res?.forcePhoneBing })
    }
  })

  //级联
  const { data: cascadeDefaultData, loading: cascadeDefaultLoading, run: runGetCascadeDefault } = useRequest(getCascadeDefault, {
    onSuccess: (cascadeRemoval) => {
      form.setFieldsValue({ cascadeRemoval: cascadeRemoval === false ? 0 : 1 })
    }
  })

  //查询是否有短信复核权限
  const { data: isCommunity, run: fetchVersion } = useRequest(
    getVersionIsCommunity,
    {
      manual: true,
    }
  );

  useEffect(() => {
    fetchVersion();
  }, []);

  const onSuccessCallback = () => {
    message.success(t('common.message.editSuccess'))
    setEditing('')
  }

  const onErrorCallback = (fileInit: any) => {
    setEditing('')
    form.setFieldsValue(fileInit)
  }

  const { loading: updateFilterResourceLoading, run: postFilterResourceRun } = useRequest(postFilterResource, {
    manual: true,
    onSuccess() {
      onSuccessCallback();
    },
    onError() {
      setEditing('');
    },
  })



  const { loading: uploadForceLoginLoading, run: postForceLoginRun } = useRequest(postForceLogin, {
    manual: true,
    onSuccess() {
      onSuccessCallback();
      runGetForceLogin();
    },
    onError() {
      onErrorCallback({
        forceOtpBing: getForceLoginData?.forceOtpBing,
        forcePhoneBing: getForceLoginData?.forcePhoneBing
      })
    }
  })

  return (
    <Spin spinning={forceLoaginLoading || cascadeDefaultLoading || uploadForceLoginLoading || updateFilterResourceLoading}
    >
      <p className={styles.settingCategory}>{t('systemManagement.system.other.OtherSetting')}</p>
      {/* 强制OTP绑定*/}
      <Form.Item
        label={
          <>
            {t('systemManagement.system.other.basic.forceOtpBing')} <Tooltip title={t('systemManagement.system.other.basic.forceOtpBing.extra')}>
              <QuestionCircleOutlined className={styles.pl5} />
            </Tooltip>
          </>
        }
      >
        <Form.Item
          name='forceOtpBing'
          valuePropName='checked'
        >
          <Switch onChange={(v) => {
            postForceLoginRun({ forceOtpBing: v, forcePhoneBing: form.getFieldValue('forcePhoneBing') })
          }}
            disabled={!editOtp} title={!editOtp ? bindTitle?.forceOtpBing : ''} />
        </Form.Item>
      </Form.Item>
      {/* 强制绑定手机 */}
      {
        isCommunity?.version !== "community" &&  !isLangEn && <Form.Item
          label={
            <>
              {t('systemManagement.system.other.basic.forcePhoneBing')} 
              <Tooltip title={t('systemManagement.system.other.basic.forcePhoneBing.tip')}>
                <QuestionCircleOutlined className={styles.pl5} />
              </Tooltip>
            </>
          }
        >
          <Form.Item
            name='forcePhoneBing'
            valuePropName='checked'
          >
            <Switch onChange={(v) => {
              postForceLoginRun({ forcePhoneBing: v, forceOtpBing: form.getFieldValue('forceOtpBing') })
            }}
              disabled={!editPhone} title={!editPhone ? bindTitle?.forcePhoneBing : ''} />
          </Form.Item>
        </Form.Item>
      }
      {/* 级联移除设置 */}
      <Form.Item
        label={t('systemManagement.system.other.sysFilterLib')}
      >
        <Input.Group compact style={{ display: "ruby-text" }}>
          <Form.Item
            name={['sysFilterLib', 'dataSource']}
            noStyle
          >
            <Select
              placeholder={t('systemManagement.system.other.filterResources.tip')}
              options={datasourceOptions}
              style={{ width: 160 }}
              onChange={(e) => getFilterResourceRun(e as any)}
            />
          </Form.Item>
          <Form.Item
            name={['sysFilterLib', 'filterResources']}
            noStyle
            style={{ marginLeft: 3 }}
          >
            <SelectWithAdd
              form={form}
              fieldKey={['sysFilterLib', 'filterResources']}
              maxTagCount={2}
              options={filterResourceOptions}
              handleSave={(e) => {

                form.validateFields().then((values) => {
                  const { dataSource, filterResources } = values?.sysFilterLib || {};
                  if (dataSource) {
                    postFilterResourceRun(dataSource, filterResources)
                  }
                })
              }}
              disabled={editing !== 'sysFilterLib'}
            />
            {
              (editing !== 'sysFilterLib') &&
              <FormOutlined
                onClick={() => {
                  setEditing('sysFilterLib')
                }}
                style={{
                  verticalAlign: 'middle',
                  marginLeft: 3,
                }}
              />
            }
          </Form.Item>
        </Input.Group>
      </Form.Item>
      {/* 系统过滤库 */}
    </Spin>
  )
})
