import React, { useContext, useState, memo } from 'react'
import {
  FormOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons'
import {
  message,
  Form,
  Typography,
  Tooltip,
  Switch,
  Select,
  InputNumber,
  Spin
} from 'antd'
import classnames from 'classnames'
import { FormInstance } from 'antd/lib/form'
import { useTranslation } from 'react-i18next'
import {
  getResultEnc,
  postResultEnc,
  getSampleCheck,
  postSampleCheck,
  getBackupValidperiod,
  updateBackupValidperiod,
  getConnectionFailedCount,
  updateConnectionFailedCount,
  getDictExecuteSetting,
  updateDictExecuteSetting,
  getApplyExportAutoDownloadSetting,
  postExportAutoDownloadSetting
} from 'src/api'
import { CustomInputNumber } from 'src/components'
import { useDispatch, useRequest } from 'src/hook'
import { setExportAutoDownload} from 'src/appPages/login/loginSlice'
import { BACKUP_VALIDPERIOD_OPTIONS } from '../constants';
import { SettingFormContext } from '../../SettingFormContext'
import styles from '../index.module.scss'

const { Text } = Typography



export const SystemFunctionModuleSetting = memo(() => {

  const { t } = useTranslation()
  const daysOptions = Object.keys(BACKUP_VALIDPERIOD_OPTIONS).map(time => ({ label: BACKUP_VALIDPERIOD_OPTIONS[time], value: Number(time) }));

  const [editing, setEditing] = useState('')
  const form = useContext(SettingFormContext) as FormInstance
 
  const dispatch = useDispatch()


  //是否自动下载
  const { data: applyExportAutoDownloaData, loading: applyExportAutoDownloadLoading, run: runGetApplyExportAutoDownloadSetting } = useRequest(getApplyExportAutoDownloadSetting, {
    onSuccess(exportAutoDownload) {
      form.setFieldsValue({ exportAutoDownload });
      dispatch(setExportAutoDownload(exportAutoDownload))
    }
  })
  

  //结果集导出加密
  const { data: resultEncData, loading: resultEncLoading, run: runGetResultEnc } = useRequest(getResultEnc, {
    onSuccess(resultEnc) {
      form.setFieldsValue({ resultEnc })
    }
  })

  //采样行数  命中率
  const { data: sampleCheckData, loading: sampleCheckLoading, run: runGetSampleCheck } = useRequest(getSampleCheck, {
    onSuccess(res) {
      form.setFieldsValue({ sampleCount: res?.sampleCount, sampleRate: res?.sampleRate })
    }
  })


  //同步数据字典任务遇到错误是否继续
  const { data: dictExecuteSettingData, loading: dictExecuteSettingLoading, run: runGetDictExecuteSetting } = useRequest(getDictExecuteSetting, {
    onSuccess(dictExecuteSetting) {
      form.setFieldsValue({ dictExecuteSetting: !!dictExecuteSetting })
    }
  })

  //查询数据备份文件有效期
  const { data: days, run: runGetBackupValidperiod } = useRequest(getBackupValidperiod, {
    onSuccess: (res) => {
      form.setFieldsValue({ days: res || -1 })
    }
  })
  //查询数据备份文件有效期
  const { data: connectionFailedCount, run: runGetConnectionFailedCount } = useRequest(getConnectionFailedCount, {
    onSuccess: (res) => {
      form.setFieldsValue({ connectionFailedCount: res || 0 })
    }
  })

  const { run: postResultEncRun } = useRequest(postResultEnc, {
    manual: true,
    onSuccess() {
      onSuccessCallback()
      runGetResultEnc()
    },
    onError() {
      onErrorCallback({ resultEnc: resultEncData })
    },
  })

  //修改数据备份时间
  const { loading: daysLoading, run: runUpdateBackupValidperiod } = useRequest(updateBackupValidperiod, {
    manual: true,
    onSuccess() {
      onSuccessCallback()
      runGetBackupValidperiod();
    },
    onError() {
      onErrorCallback({ days: days || -1 })
    },
  })


  //更新连接失败次数
  const { loading: connectionFailedCountLoading, run: runUpdateConnectionFailedCount } = useRequest(updateConnectionFailedCount,
    {
      manual: true,
      onSuccess: () => {
        onSuccessCallback()
        runGetConnectionFailedCount()
      },
      onError: () => {
        onErrorCallback({ connectionFailedCount: connectionFailedCount || 0 })
      }
    }
  );

  const onSuccessCallback = () => {
    message.success(t('common.message.editSuccess'))
    setEditing('')
  }

  const onErrorCallback = (fileInit: any) => {
    setEditing('')
    form.setFieldsValue(fileInit)
  }
  

  const { run: runPostExportAutoDownloadSetting } = useRequest(postExportAutoDownloadSetting, {
    manual: true,
    onSuccess() {
      onSuccessCallback()
      runGetApplyExportAutoDownloadSetting();
    },
    onError() {
      onErrorCallback({ exportAutoDownload: applyExportAutoDownloaData })
    },
  })

  const { run: postSampleCheckRun } = useRequest(postSampleCheck, {
    manual: true,
    onSuccess() {
      onSuccessCallback()
      runGetSampleCheck();
    },
    onError() {
      onErrorCallback({ sampleCount: sampleCheckData?.sampleCount, sampleRate: sampleCheckData?.sampleRate })
    },
  })

  const { loading: updateDictExecLoading, run: runUpdateDictExecuteSetting } = useRequest(updateDictExecuteSetting, {
    manual: true,
    onSuccess() {
      onSuccessCallback()
      runGetDictExecuteSetting()
    },
    onError() {
      onErrorCallback({ dictExecuteSetting: !!dictExecuteSettingData })
    },
  })


  const onSaveSampleCount = (e: React.ChangeEvent<HTMLInputElement>) => {
    const val = e.target.value;
    form.validateFields().then(() => {
      postSampleCheckRun(val, form.getFieldValue('sampleRate') || 80)
    })
  }

  const onSaveSampleRate = (e: React.ChangeEvent<HTMLInputElement>) => {
    const val = e.target.value;
    form.validateFields().then(() => {
      postSampleCheckRun(form.getFieldValue('sampleCount') || 100, val)
    })
  }

  const onSaveConnectionFailedCount = () => {
    form.validateFields(['connectionFailedCount']).then((val) => {

      if (val?.connectionFailedCount === null) {
        return message.info(t('systemManagement.system.other.connectionFailedCount.tip'))
      }
      runUpdateConnectionFailedCount(val?.connectionFailedCount)
    })
  }

  return (
    <Spin
      spinning={dictExecuteSettingLoading 
        || resultEncLoading || resultEncLoading || sampleCheckLoading || connectionFailedCountLoading || daysLoading
        || updateDictExecLoading || applyExportAutoDownloadLoading
      }
    >
      <p className={styles.settingCategory}>{t('systemManagement.system.other.sysFunctionModuleSetting')}</p>
      {/* 数据被问文件有效期设置 */}
      <Form.Item
        label={
          <>
            {t('systemManagement.system.other.days')}
            <Tooltip title={t('systemManagement.system.other.days.extra')}>
              <QuestionCircleOutlined className={styles.pl5} />
            </Tooltip>
          </>
        }
      >
        {
          editing === 'days' ?
            <Form.Item name="days"
              noStyle
            >
              <Select
                placeholder={t('systemManagement.system.other.days.tip')}
                options={daysOptions}
                onChange={(val: any) => { runUpdateBackupValidperiod({ days: val.toString() }) }}
                onBlur={() => { setEditing('') }}
              />
            </Form.Item>
            :
            (
              <div style={{ display: 'flex' }}>
                <Text className={classnames(styles.w300, styles.disInB)}>{BACKUP_VALIDPERIOD_OPTIONS[days]}</Text>
                <FormOutlined onClick={() => { setEditing('days') }} />
              </div>
            )}
      </Form.Item>
  
     
       {/* 导出文件时是否自动下载 */}
       <Form.Item label={t('systemManagement.system.other.autoDownload')}>
        <Form.Item
          noStyle
          name='exportAutoDownload'
          valuePropName='checked'
        >
          <Switch onChange={(v) => runPostExportAutoDownloadSetting(v)} />
        </Form.Item>
       </Form.Item>
      {/* 结果集导出加密 */}
      <Form.Item
        label={
          <>
            {t('systemManagement.system.other.resultEnc')}
            <Tooltip title={t('systemManagement.system.other.resultEnc.tip')}>
              <QuestionCircleOutlined className={styles.pl5} />
            </Tooltip>
          </>
        }
      >
        <Form.Item
          noStyle
          name='resultEnc'
          valuePropName='checked'
        >
          <Switch onChange={(v) => postResultEncRun(v)} />
        </Form.Item>
      </Form.Item>

      {/* 采样行数 */}
      <Form.Item
        label={t('common.text.sampleCount')}
      >
        {
          (editing === 'sampleCount') ?
            <Form.Item
              name="sampleCount"
              initialValue={100}
              rules={[
                {
                  required: true,
                  message: t('common.text.sampleCount.hint'),
                  min: 0,
                  max: 100,
                  type: 'number',
                  transform: (v) => {
                    return v !== '' ? Number(v) : null;
                  },
                },
              ]}
            >
              <CustomInputNumber
                autoFocus
                placeholder={t('common.text.sampleCount.tip')}
                onBlur={(e) => onSaveSampleCount(e)}
                style={{ width: '100%' }}
                type={'int'}
              />
            </Form.Item>
            :
            (
              <div style={{ display: 'flex' }}>
                <Text className={classnames(styles.w300, styles.disInB)}>{sampleCheckData?.sampleCount}</Text>
                <FormOutlined onClick={() => {
                  setEditing('sampleCount')
                }} />
              </div>
            )}
      </Form.Item>
      {/* 命中率 */}
      <Form.Item label={t('common.text.sampleRate')}>
        {
          editing === 'sampleRate' ?
            <Form.Item
              name="sampleRate"
              initialValue={80}
              rules={[
                {
                  required: true,
                  message: t('common.text.sampleCount.hint'),
                  min: 0,
                  max: 100,
                  type: 'number',
                  transform: (v) => {
                    return v !== '' ? Number(v) : null;
                  },
                },
              ]}
            >
              <CustomInputNumber
                autoFocus
                placeholder={t('common.text.sampleRate.tip')}
                onBlur={(e) => onSaveSampleRate(e)}
                style={{ width: '100%' }}
                type={'positiveNum'}
              />
            </Form.Item>
            :
            (
              <div style={{ display: 'flex' }}>
                <Text className={classnames(styles.w300, styles.disInB)}>{sampleCheckData?.sampleRate + " %"}</Text>
                <FormOutlined onClick={() => { setEditing('sampleRate') }} />
              </div>
            )}
      </Form.Item>
      <Form.Item
        label={
          <>
            {t('systemManagement.system.other.connectionFailedCount')}
            <Tooltip title={t('systemManagement.system.other.connectionFailedCount.extra')}>
              <QuestionCircleOutlined className={styles.pl5} />
            </Tooltip>
          </>
        }
      >
        <Form.Item
          name="connectionFailedCount"
          noStyle
          hidden={!(editing === 'connectionFailedCount')}
          rules={[{ min: 0, max: 1000, type: 'number', message: t('common.text.sampleCount.hint') }]}
        >
          <InputNumber
            autoFocus
            min={0}
            placeholder={t('systemManagement.system.other.connectionFailedCount.tip')}
            onBlur={() => onSaveConnectionFailedCount()}
            style={{ width: '100%' }}
          />
        </Form.Item>
        {editing !== 'connectionFailedCount' && (
          <div style={{ display: 'flex' }}>
            <Text className={classnames(styles.w300, styles.disInB)}>
              {connectionFailedCount}
            </Text>
            <FormOutlined
              onClick={() => {
                setEditing('connectionFailedCount')
              }}
            />
          </div>
        )}
      </Form.Item>
      <Form.Item label={t('systemManagement.personManagement.continue.sync.error')}>
        <Form.Item
          noStyle
          name='dictExecuteSetting'
          valuePropName='checked'
        >
          <Switch onChange={(v) => runUpdateDictExecuteSetting(v)} />
        </Form.Item>
      </Form.Item>
    </Spin>
  )
})
