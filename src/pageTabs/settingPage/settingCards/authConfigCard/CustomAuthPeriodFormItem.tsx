import React from "react";
import { <PERSON>, But<PERSON>, Table, Space, Switch,message, Typography, Tooltip, Popconfirm } from "antd";
import type { ColumnsType } from 'antd/es/table';
import { PlusOutlined } from '@ant-design/icons';
import { useRequest, useDispatch } from "src/hook";
import {
  IValidTimeItem,
  getValidaTimeList,
  deleteValidaTime,
  moveValidTimeItem,
  updateValidTime
} from 'src/api';
import { setCustomAuthorizationPeriod } from 'src/appPages/login/loginSlice'
import AddOrEditPermPeriodModal from './AddOrEditAuthPeriodModal';
import { MoveDirection } from './types'
import classNames from "classnames";
import { ALL_VALID_TIME_UNIT, SPECIAL_ECHO_FIELD } from './constants'
import { useTranslation } from "react-i18next";
import { formattedValidTimeData, FormattedTimeDataItem } from './utils';
import styles from './index.module.scss'


interface IValidTimeMoreFields extends IValidTimeItem {
  isFirst?: boolean;
  isLast?: boolean;
}

export const CustomAuthPeriodFormItem = () => {

  const dispatch = useDispatch();
  const { t } = useTranslation();

  const [addOrEditModalVisible, setAddOrEditModalVisible] = React.useState(false);
  const [editPermPeriodItem, setEditPermPeriodItem] = React.useState<any | null>(null);

  //列表
  const { data: validTimeList = [], loading: validTimeListLoading, refresh: refreshValidTimeList } = useRequest(getValidaTimeList,
    {
      onSuccess: (res = []) => {
        let timeRangeOptions: FormattedTimeDataItem[] = [];
        if (res?.length) {
           timeRangeOptions = formattedValidTimeData(res);
        }
       
        dispatch(setCustomAuthorizationPeriod(timeRangeOptions))
      },
      formatResult(res = []) {
        return res.map((item: IValidTimeItem, index: number) => ({
          ...item,
          ...(index === 0 ? { isFirst: true } : {}),
          ...(index === (res?.length - 1) ? { isLast: true } : {}),
        }))
      }
    });

  //删除
  const { run: runDeleteValidaTime } = useRequest(deleteValidaTime, {
    manual: true,
    onSuccess: () => {
      message.success(t('common.message.delete_success'));
      refreshValidTimeList();
    }
  })
  //移动
  const { run: runMoveValidaTime } = useRequest(moveValidTimeItem, {
    manual: true,
    onSuccess: () => {
      message.success(t('systemManagement.system.authMenu.customAuthPeriod.moveSuccessful'));
      refreshValidTimeList();
    }
  })
  const onEdit = (record: any) => {
    setAddOrEditModalVisible(true);
    setEditPermPeriodItem(record);
  }


  const onCancelModal = () => {
    setAddOrEditModalVisible(false);
    setEditPermPeriodItem(null);
  }

  const onMoveUpOrDown = (sourceId: number, type: MoveDirection) => {
  
    const sourceIndex = validTimeList.findIndex(item => item?.id === sourceId);
    const targetItem = type === 'up' ? validTimeList[sourceIndex - 1] : validTimeList[sourceIndex + 1];
    if (targetItem?.id) {
      runMoveValidaTime({ sourceId, targetId: targetItem.id })
    }
  }

  const onChangeTimeStatus = (record: IValidTimeItem, status: boolean) => {
    updateValidTime({...record, timeStatus: status }).then(() => {
      message.success(t('common.message.edit.success'));
      refreshValidTimeList();
    })
  }

  return (
    <div className={styles.customPeriodWrap}>
      <Form.Item
        colon={false}
        label={t('systemManagement.system.authMenu.customAuthPeriod.label')}
        wrapperCol={{ span: 4 }}
      >
        <Button
          type='link'
          icon={<PlusOutlined />}
          onClick={() => setAddOrEditModalVisible(true)}
        >
          {t('common.btn.add')}
        </Button>
      </Form.Item>
      <div className={styles.table}>

        <Table
          size='small'
          loading={validTimeListLoading}
          columns={columns({
            onEdit,
            onDelete: (id: number) => runDeleteValidaTime({ id }),
            onMoveUpOrDown,
            onChangeTimeStatus
          }) as ColumnsType<any>}
          dataSource={validTimeList}
          pagination={false}
        scroll={{ y: `calc(100vh - 560px)` }} //高度注意下测试
        />
      </div>
      {
        addOrEditModalVisible &&
        <AddOrEditPermPeriodModal
          editPermPeriodItem={editPermPeriodItem}
          onCancel={onCancelModal}
          onRefresh={refreshValidTimeList}
        />
      }
    </div>
  )
}


const columns = ({
  onEdit,
  onDelete,
  onMoveUpOrDown,
  onChangeTimeStatus
}: {
  onEdit: (record: IValidTimeMoreFields) => void;
  onDelete: (id: number) => void;
  onMoveUpOrDown: (id: number, type: MoveDirection) => void;
  onChangeTimeStatus: (record: IValidTimeItem, status: boolean) => void;
}) => {

  const { t } = useTranslation();
   
  return [
    {
      dataIndex: 'timeName',
      title: t('systemManagement.system.authMenu.name.label'),
      key: 'timeName',
      width: '40%',
      render: (val: string) => (
        <Tooltip title={val}>
          <Typography.Text ellipsis className={classNames(styles.permName, 'linkStyle')}>{val}</Typography.Text>
        </Tooltip>)
    },
    {
      dataIndex: 'timeValue',
      title: t('systemManagement.system.authMenu.parameter.label'),
      key: 'timeValue',
      render: (val: string, record: IValidTimeItem) => {
        
        let parameter = `${val}${ALL_VALID_TIME_UNIT[record?.timeValueUnit]}`;
   
        if (SPECIAL_ECHO_FIELD.includes(record?.timeValueUnit)) {
          return ALL_VALID_TIME_UNIT[record?.timeValueUnit]
        }
        return (
          <Typography.Text ellipsis>{parameter}</Typography.Text>
        )
      } 
    },
    {
      dataIndex: 'timeStatus',
      title: t('systemManagement.system.authMenu.status.label'),
      key: 'timeStatus',
      render: (val: boolean, record: IValidTimeItem) => {
        return (
          <Switch checked={val} disabled={!record?.timeEdit} onChange={(v: boolean) => onChangeTimeStatus(record, v)}/>
        )
      }
    },
    {
      dataIndex: 'action',
      title: t('common.text.action'),
      width: 200,
      key: 'action',
      fixed: 'right',
      render: (val: string, record: IValidTimeMoreFields) => {
        //disbaled
        return (
          <Space>
            {
              !record?.isFirst &&
              // 第一个禁止上移
              <Button type='link' className="padding0" onClick={() => record?.id && onMoveUpOrDown(record.id, 'up')}>{t('systemManagement.system.authMenu.customAuthPeriod.up')}</Button>

            }
            {
              !record?.isLast &&
              // 最后一个禁止下移
              <Button type='link' className="padding0" onClick={() => record?.id && onMoveUpOrDown(record.id, 'down')}>{t('systemManagement.system.authMenu.customAuthPeriod.down')}</Button>
            }
            {
              record?.timeEdit &&
              <>
                <Button type='link' className="padding0" onClick={() => onEdit(record)}>{t('common.btn.edit')}</Button>
                <Popconfirm
                  title={t('common.modal.delete.content')}
                  onConfirm={() => record?.id && onDelete(record.id)}
                  okText={t('common.btn.confirm')}
                  cancelText={t('common.btn.cancel')}
                >
                  <Button type='link' className="padding0">{t('common.btn.delete')}</Button>
                </Popconfirm>
              </>
            }
          </Space>
        )
      }
    },
  ]
} 
