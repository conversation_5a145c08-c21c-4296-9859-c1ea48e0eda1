import React from 'react'
import { Form } from 'antd'
import {
  EmailCard,
  OtherCard,
  PasswordCard,
  LogoImageCard,
  AuditLogConfigCard,
  AuthConfigCard
} from './settingCards'
import { useSelector } from 'src/hook'
import { FormLayout } from './constants'
import { SettingFormContext } from './SettingFormContext'
import { ParsingConfigurationCard } from './settingCards/ParsingConfigurationCard'
import { AuthorizationCard } from './settingCards/AuthorizationCard'
import { SingleSignOnCard } from './settingCards/SingleSignOnCard'
import { AppSecret }  from './settingCards/appSecretPage'
import { WatermarkCard } from './settingCards/WatermarkCard'
import { UploadCqDataCard } from './settingCards/UploadCqDataCard';
import { AccessPolicyCard } from './settingCards/AccessPolicyCard';
import { AlarmConfigCard } from './settingCards/AlarmConfigCard'
import { SmsGatewayCard } from './settingCards/SmsGatewayCard'

interface IProps {
  activeKey: string
  subTabKey: string
}
export const SettingBasicPage = (props: IProps) => {
  const { activeKey, subTabKey } = props
  const [form] = Form.useForm()
  const isLangEn = useSelector(state => state.login.locales) === 'en';

  return (
    <SettingFormContext.Provider value={form}>
      <Form component={false} form={form} {...FormLayout} labelCol={{span: isLangEn ? 8 : 5 }}>
        {
          !!activeKey && activeKey === 'LogoImageCard' && <LogoImageCard />
        }
         {
          !!activeKey && activeKey === 'AppSecretPage' &&  <AppSecret />
        }
        {
          !!activeKey && activeKey === 'EmailCard' && <EmailCard />
        }
        {
          !!activeKey && activeKey === 'SmsGatewayCard' && <SmsGatewayCard subTabKey={subTabKey} />
        }
        {
          !!activeKey && activeKey === 'SingleSignOnCard' &&  <SingleSignOnCard />
        }
        {
          !!activeKey && activeKey === 'OtherCard' && <OtherCard />
        }
        {
          !!activeKey && activeKey === 'PasswordCard' && <PasswordCard />
        }
        {
          !!activeKey && activeKey === 'ParsingCard' && <ParsingConfigurationCard />
        }
        {
          !!activeKey && activeKey === 'AuthorizationCard' && <AuthorizationCard />
        }
        {
          !!activeKey && activeKey === 'WatermarkCard' && <WatermarkCard />
        }
        {
          !!activeKey && activeKey === 'UploadCqDataCard' && <UploadCqDataCard />
        }
        {
          !!activeKey && activeKey === 'AccessPolicyCard' && <AccessPolicyCard />
        }
        {
          !!activeKey && activeKey === 'AlarmConfigCard' && <AlarmConfigCard />
        }
        {
          !!activeKey && activeKey === 'AuditLogConfigCard' && <AuditLogConfigCard />
        }
        {
          !!activeKey && activeKey === 'PermissionConfigCard' && <AuthConfigCard/>
        }
      </Form>
    </SettingFormContext.Provider>
  )
}
