import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { SettingBasicPage } from './SettingBasicPage'
import { Layout, Breadcrumb, Tabs } from 'antd'
import { useRequest, useSelector, useDispatch } from 'src/hook'
import { Iconfont } from 'src/components'
import 'src/styles/layout.scss'
import styles from './settingPage.module.scss'
import {getDataMigration, getVersionIsCommunity} from "../../api";
import {
  setSettingPageState, 
  setSettingPageDetialParams 
} from 'src/pageTabs/settingPage/settingPageSlice';
import { AddChannelModal } from "src/pageTabs/settingPage/settingCards/SmsGatewayCard/AddChannelModal";
const { Header, Sider, Content } = Layout

let dataMigration = false;

getDataMigration().then((enable) => {
  dataMigration = enable;
})

export const SettingPage = () => {
  const dispatch = useDispatch()
  const { settingPageState, settingPageDetialParams } = useSelector((state) => state.settingPage)
  const { t } = useTranslation();
  const {
    userInfo
  } = useSelector((state) => state.login)
  const isLangEn = useSelector(state => state.login.locales) === 'en'
  const [tabsKey, setTabesKey] = useState(userInfo?.version !== 'community' ? 'LogoImageCard' : 'EmailCard')
  const [subTabKey, setSubTabKey] = useState<string>('')

  const showAuditLogConfigCard = true;

  //获取version
  const { data: isCommunity, run: fetchVersion } = useRequest(
    getVersionIsCommunity,
    {
      manual: true,
    }
  );

  useEffect(() => {
    fetchVersion();
  }, []);

  const resetPageState = () => {
    dispatch(setSettingPageState(''))
    dispatch(setSettingPageDetialParams({}))
  }

  useEffect(()=>{
    return ()=>{
      resetPageState()
    }
  },[])

  useEffect(() => {
    const state = settingPageDetialParams as any
    if (state?.tabKey) {
      setTabesKey(state?.tabKey)
      setSubTabKey(state?.subTabKey)
      resetPageState()
    } else {
      setSubTabKey('')
    }
  }, [tabsKey])
  
  //全局搜索定位
  useEffect(() => {
    
    const state = settingPageDetialParams as any;
    if (state?.globalSearchTabKey) {
      setTabesKey(state.globalSearchTabKey);
    }
  }, [settingPageDetialParams])
  
  const SettingTabs = (
    <Tabs
      className={styles.settingPagesTabs}
      tabPosition={'left'}
      type="card"
      activeKey={tabsKey}
      onChange={(e) => {
        setTabesKey(e)
      }}
    >
      {
        userInfo?.version !== 'community' && <Tabs.TabPane tab={
          <>
            <Iconfont type="icon-UIpeizhi"></Iconfont>
            <span>{t('systemManagement.system.ui')}</span>
          </>
        } key="LogoImageCard" />
      }
       <Tabs.TabPane tab={
        <>
          <Iconfont type="icon-jichupeizhi"></Iconfont>
          <span>{t('systemManagement.system.secretKey')}</span>
        </>
      } key="AppSecretPage" />
      <Tabs.TabPane tab={
        <>
          <Iconfont type="icon-youxiangshezhi"></Iconfont>
          <span>{t('systemManagement.system.email')}</span>
        </>
      } key="EmailCard" />
      {
        userInfo?.version !== 'community' && !isLangEn  && <Tabs.TabPane tab={
          <>
            <Iconfont type="icon-duanxinshezhi"></Iconfont>
            <span>{t('systemManagement.system.sms')}</span>
          </>
        } key="SmsGatewayCard" />
      }
      {
        userInfo?.version !== 'community' && <Tabs.TabPane tab={
          <>
            <Iconfont type="icon-dandiandenglushezhi"></Iconfont>
            <span>{t('systemManagement.system.sso')}</span>
          </>
        } key="SingleSignOnCard" />
      }
      <Tabs.TabPane tab={
        <>
          <Iconfont type="icon-jichupeizhi"></Iconfont>
          <span>{t('systemManagement.system.basic')}</span>
        </>
      } key="OtherCard" />
      <Tabs.TabPane tab={
        <>
          <Iconfont type="icon-mimashezhi"></Iconfont>
          <span>{t('systemManagement.system.password')}</span>
        </>
      } key="PasswordCard" />
      <Tabs.TabPane tab={
        <>
          <Iconfont type="icon-jiexipeizhi"></Iconfont>
          {t('systemManagement.system.analysis')}
        </>
      } key="ParsingCard" />
      {
        isCommunity?.version !== "community" &&
        <Tabs.TabPane tab={
          <>
            <Iconfont type="icon-shouquanxinxi"></Iconfont>
            <span>{t('systemManagement.system.auth')}</span>
          </>
        } key="AuthorizationCard" />
      }
      <Tabs.TabPane tab={
        <>
          <Iconfont type="icon-shuiyinshezhi"></Iconfont>
          <span>{t('systemManagement.system.watermark')}</span>
        </>
      } key="WatermarkCard" />
      <Tabs.TabPane tab={
        <>
          <Iconfont type="icon-fangwenshezhi"></Iconfont>
          <span>{t('systemManagement.system.access')}</span>
        </>
      } key="AccessPolicyCard" />
      {
        (userInfo?.version !== 'community' && dataMigration) && <Tabs.TabPane tab={
            <>
              <Iconfont type="icon-jiexipeizhi"></Iconfont>
              {t('systemManagement.system.dataMigration')}
            </>
          } key="UploadCqDataCard" />
      }
      {
        <Tabs.TabPane tab={
            <>
              <Iconfont type="icon-gaojingpeizhi"></Iconfont>
              {t('systemManagement.system.alarm')}
            </>
          } key="AlarmConfigCard" />
      }
      {
        showAuditLogConfigCard && userInfo?.version !== 'community' && <Tabs.TabPane tab={
          <>
            <Iconfont type="icon-rizhibaolupeizhi"></Iconfont>
            <span>{t('systemManagement.system.log')}</span>
          </>
        } key="AuditLogConfigCard" />
      }
      <Tabs.TabPane 
        key="PermissionConfigCard"
        tab={
          <>
            <Iconfont type="icon-quanxianxiangguanshezhi"/>
            {t('systemManagement.system.authMenu.title')}
          </>
        }  />
    </Tabs>
  )

  // 渲染系统设置-短信网关设置-添加短信通道模板
  if(settingPageState === 'addChannel'){
    return <AddChannelModal />
  }

  return (
    <Layout className={`${styles.settingPageWrap} cq-container`}>
      <Header className="breadcrumb-header">
        <Breadcrumb className="breadcrumb" separator=''>
          <Breadcrumb.Item>{t('systemManagement.title')}</Breadcrumb.Item>
          <Breadcrumb.Separator>|</Breadcrumb.Separator>
          <Breadcrumb.Item>{t('systemManagement.system')}</Breadcrumb.Item>
        </Breadcrumb>
      </Header>
      <Layout className="cq-main setting-warp" style={{padding: '0 10px 0 0'}}>
        <Sider className="cq-aside" width={isLangEn ? 300 : 246}>
          <section className="cq-card" style={{ background: '#F7F9FC', border: 'none' }}>
            <div className={styles.settingHeaderTitle} >
              {t('systemManagement.system')}
            </div>
            <div className="cq-card__content" style={{ width: '94%', margin: '0 auto' }}>
              {SettingTabs}
            </div>
          </section>
        </Sider>
        <Content className="cq-content" id="anchor-box" style={{marginLeft: '10px'}}>
          <div className='cq-content-card' style={{}}>
            <SettingBasicPage activeKey={tabsKey} subTabKey={subTabKey}/>
          </div>
        </Content>
      </Layout>
    </Layout>
  )
}
