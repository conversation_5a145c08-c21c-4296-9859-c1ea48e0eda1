@import 'src/styles/variables';

.breadcrumbLink {
  cursor: pointer;
  &:hover {
    color: #555;
  }
}
.options {
  cursor: pointer;
  color: var(--primary-color)
}
.btnDisabled {
  cursor: not-allowed !important;
  background-color: #B3B3B3 !important;
}
.disabled {
  cursor: not-allowed;
  color: #B3B3B3;
}
.cursorPointer {
  cursor: pointer;
}
.db {
  display: block;
}
.w16 {
  width: 16px;
}
.w60 {
  width: 60px;
}
.ml4 {
  margin-left: 4px;
}
.ml10 {
  margin-left: 10px;
}
.mlr10 {
  margin: 0 10px;
}
.ml14 {
  margin-left: 14px;
}
.ml16 {
  margin-left: 16px;
}
.ml20 {
  margin-left: 20px;
}
.ml36 {
  margin-left: 36px;
}
.ml40 {
  margin-left: 40px;
}
.fwb {
  font-weight: bold;
}
.fs12 {
  font-size: 12px;
  line-height: 14px;
}
.fs14 {
  font-size: 14px;
}
.mt10 {
  margin-top: 10px;
}
.mt20 {
  margin-top: 20px;
}
.mtb10 {
  margin: 10px 0;
}
.mb4 {
  margin-bottom: 4px;
}
.mb10 {
  margin-bottom: 10px;
}
.mb20 {
  margin-bottom: 20px;
}
.mr2 {
  margin-right: 2px;
}
.mr4 {
  margin-right: 4px;
}
.mr8 {
  margin-right: 8px;
}
.mr10 {
  margin-right: 10px;
}
.mr16 {
  margin-right: 16px;
}
.mr20 {
  margin-right: 20px;
}
.mr24 {
  margin-right: 24px;
}
.mr32 {
  margin-right: 32px;
}
.padding4 {
  padding: 4px;
}

.padding0 {
  padding: 0px 0px;
}
.colore999 {
  color: #999;
}
.color667084 {
  color: #667084;
}

.color3357ff {
  color: #3357ff;
}

.linkStyle {
  color: #3357ff;
  cursor: pointer;
}

.nonmodifiableColor {
  color: #868FA3;
  cursor: pointer;
}
.dropDownDisabled {
  background-color: #f3f3f3 !important;
  color: #cacadd  !important;
  border-color: #cacadd  !important;
}
.tc {
  text-align: center;
}

.linkText {
  color: #3262FF;
  cursor: pointer;
}

.globalSearchRowSelected {
  background-color: #91d5ff;
  >td {
    background-color: #91d5ff;
  }
}

.lh18 {
  line-height: 18px;
}
.flex {
  display: flex;
}
.flex1 {
  flex: 1
}
.flexAlignCenterJustifyEnd {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.flexAlignCenter{
  display: flex;
  align-items: center;
}
.flexJustifyCenter {
  display: flex;
  justify-content: center;
}
.flexAlignCenterWrap {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.flexJustifyEnd {
  display: flex;
  justify-content: flex-end;
}
.flexSpaceBetween {
  display: flex;
  justify-content: space-between;
}
.flexAlignCenterBetween {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.textOverflow {
  white-space: nowrap; 
  overflow: hidden;
  text-overflow: ellipsis;
}
.execSuccessLine {
  background-color: rgba(171, 255, 46, 0.2);
}

.messageRedLabel {
  height: 18px;
  padding: 0px 4px;
  font-size: 12px;
  background-color: red;
  color: #fff;
  border-radius: 4px;
}
.execErrorLine {
  color: #e8180f !important;
  background-color: rgba(232, 24, 15, 0.2);
}
.execSuccessBar {
  background: url(../assets/img/right.png) right center/28px 28px no-repeat;
}
.execErrorBar {
  background: url(../assets/img/error.png) right center/28px 28px no-repeat;
}

/* monaco style for enableSplitViewResizing start */
:root {
	--sash-size: 4px;
}
.monaco-sash.vertical {
	cursor: ew-resize;
	top: 0;
	width: var(--sash-size);
	height: 100%;
}
.monaco-sash.horizontal {
	cursor: ns-resize;
	left: 0;
	width: 100%;
	height: var(--sash-size);
}
.monaco-sash:not(.disabled).orthogonal-start::before, .monaco-sash:not(.disabled).orthogonal-end::after {
	content: ' ';
	height: calc(var(--sash-size) * 2);
	width: calc(var(--sash-size) * 2);
	z-index: 100;
	display: block;
	cursor: all-scroll; position: absolute;
}
.monaco-sash.orthogonal-start.vertical::before { left: -calc(var(--sash-size) / 2); top: -var(--sash-size); }
.monaco-sash.orthogonal-end.vertical::after { left: -calc(var(--sash-size) / 2); bottom: -var(--sash-size); }
.monaco-sash.orthogonal-start.horizontal::before { top: -calc(var(--sash-size) / 2); left: -var(--sash-size); }
.monaco-sash.orthogonal-end.horizontal::after { top: -calc(var(--sash-size) / 2); right: -var(--sash-size); }
/* end */

.customReadOnlyInput { 
  border: none !important;
  box-shadow: none !important;
  &:hover,
  &:focus {
    border: none !important;
    box-shadow: none !important;
    -webkit-box-shadow: none !important;
  }
}